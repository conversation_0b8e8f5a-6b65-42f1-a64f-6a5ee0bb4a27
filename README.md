# CodeBake

*An AI-Native Project Management Platform for Developer Teams*

## Vision

CodeBake reimagines project management for the AI era. Instead of forcing developers to context-switch between code and project tracking, we bring intelligent project management directly into your development workflow.

## What Makes CodeBake Different

### AI-First Design
- **Bring Your Own Agent (BYOA)**: Connect your existing AI assistants (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.) to automatically manage tasks, create branches, and update project status
- **Integrated AI Features**: Built-in task summarization, comment analysis, and project health insights for the entire team
- **Human-AI Collaboration**: Every feature is designed for seamless collaboration between team members and AI agents

### Developer-Centric Workflow
- **Code-First Approach**: Tasks follow your code, not the other way around
- **Native Git Integration**: Branches, pull requests, and commits automatically drive task status
- **Real-Time Collaboration**: Live Kanban boards with instant updates and team presence
- **Zero Context Switching**: Manage projects without leaving your development environment

### Built for Modern Teams
- **Multi-Tenant Architecture**: Accounts → Teams → Projects → Tasks hierarchy
- **Distributed-First**: Async collaboration tools for remote and hybrid teams
- **Developer Experience**: Keyboard shortcuts, markdown everywhere, API-first design
- **Scalable**: From small startups to 100+ developer organizations

## Core Philosophy

We're not building another feature-heavy project management tool. CodeBake creates a new workflow paradigm where:

- **AI agents are productive team members**, not just assistants
- **Project management adapts to your code workflow**, not vice versa
- **Real-time collaboration happens naturally** without forced synchronization
- **Context stays with the work** through intelligent automation

## The Future of Development

CodeBake represents the next evolution in developer tooling—where artificial intelligence, version control, and project management converge into a seamless experience. Whether you're a solo developer or part of a large engineering organization, CodeBake eliminates the friction between planning, coding, and shipping.

## Getting Started with Docker

*No Elixir or Mix installation required! Run CodeBake using Docker.*

### Prerequisites

- [Docker](https://docs.docker.com/get-docker/) (version 20.10 or later)
- [Docker Compose](https://docs.docker.com/compose/install/) (version 2.0 or later)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/MisfitLab/codebake.git
   cd codebake
   ```

2. **Start the application**
   ```bash
   docker-compose up app
   ```

3. **Access the application**
   - Open your browser to [http://localhost:4000](http://localhost:4000)
   - The app will automatically set up the database and seed initial data

### Demo Login

To explore the sample project, use these credentials:

- **Email:** `<EMAIL>`
- **Password:** Use the "Sign in with magic link" option (no password required)

You'll have access to:
- **Account:** Mediastable
- **Sample Project:** "Reanimation Research Project"
- **Demo Tasks:** Various tasks showing different statuses and project workflow

That's it! CodeBake is now running with all dependencies containerized.

### What's Running

The Docker setup includes:

- **PostgreSQL Database** (port 15432) - Persistent data storage
- **LocalStack S3** (port 14566) - File attachment storage emulator
- **Phoenix Application** (port 4000) - The main CodeBake web application

### Development Commands

**View application logs:**
```bash
docker-compose logs -f app
```

**Reset the database:**
```bash
docker-compose --profile tools up db-reset
```

**Run tests:**
```bash
docker-compose --profile tools up test
```

**Generate ERD diagram:**
```bash
docker-compose --profile tools up erd
```

**Seed the database with sample data:**
```bash
docker-compose --profile tools up seeder
```

### Stopping the Application

**Stop all services:**
```bash
docker-compose down
```

**Stop and remove all data (complete reset):**
```bash
docker-compose down -v
```

### Troubleshooting

**If you encounter port conflicts:**
- PostgreSQL uses port 15432 (not the default 5432)
- LocalStack uses port 14566 (not the default 4566)
- Phoenix uses port 4000

**If the app fails to start:**
1. Ensure Docker is running
2. Check that ports 4000, 15432, and 14566 are available
3. Try a complete reset: `docker-compose down -v && docker-compose up app`

**View detailed logs:**
```bash
docker-compose logs app postgres localstack
```

---

*Ready to transform your development workflow? CodeBake is currently in active development.*
