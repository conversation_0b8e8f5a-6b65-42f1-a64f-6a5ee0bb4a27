# Product Requirements Document (PRD)
# AI-Native Project Management Platform - CodeBake

## Executive Summary

Build an AI-native project management platform that integrates deeply with developer workflows. The system uses a multi-tenant architecture (Accounts → Teams → Projects → Tasks) with real-time Kanban boards, task discussions, and seamless Git provider integration (GitHub/GitLab). 

The platform offers a revolutionary hybrid AI model:
- **Bring Your Own Agent (BYOA)**: A hosted MCP (Model Context Protocol) endpoint allows developers' existing AI assistants (in Cursor, Zed, etc.) to interact with and automate project management tasks
- **Managed In-App AI**: The web platform provides integrated AI features (task and comment summarization) for all team members

Ship v1 with working MCP integration for external agents, core in-app AI summarization, Git workflow automation, real-time Kanban collaboration, and flat comment discussions. Advanced features are strategically deferred to ensure a lean, focused MVP.

**Core Differentiator**: First project management tool where AI is a true first-class participant, both as an external agent developers already use and as an integrated assistant for the entire team. This dual approach embeds deeply in power-user workflows while providing accessible AI value to everyone.

## Product Vision & Strategy

### Vision Statement
Create the IDE for project management - where code, tasks, and AI collaboration converge seamlessly.

### Strategic Positioning
- **Not Another PM Tool**: We're not competing with <PERSON>ra/<PERSON>ana on feature count. We're building a new workflow paradigm
- **AI-Native from Day 1**: Every feature is designed for human-AI collaboration, supporting both external and internal AI agents
- **Code-First Workflow**: Tasks follow code, not vice versa
- **Developer Experience Focus**: Built by developers, for developers

### Key Differentiators
1. **Hybrid AI Model**:
   - BYOA via MCP: Developers connect their personal AI assistants for unparalleled workflow integration
   - Integrated Managed AI: Web app provides AI summarization accessible to the entire team

2. **Native Git Workflows**: Branches, PRs, and commits drive task status automatically

3. **Real-Time Everything**: Live Kanban updates, presence, and comment streams

4. **Developer-First UX**: Keyboard shortcuts, markdown everywhere, API-first design

## Goals & Non-Goals

### Goals (v1)
- Enable developers' existing AI agents to be productive team members via MCP
- Provide immediate, accessible AI value to all personas through task/comment summarization
- Eliminate context switching between code and project management
- Provide a single source of truth for engineering work
- Support distributed teams with async-first design
- Scale from small startups to mid-size engineering orgs (100+ devs)

### Non-Goals (v1)
- Enterprise governance features (SOC2 compliance is v2)
- Complex resource planning or capacity management
- Time tracking, budgeting, or invoicing
- Gantt charts, roadmaps, or portfolio management
- Mobile apps (responsive web only)
- Custom workflow scripting (fixed workflows for v1)
- CLI access (deferred to v1.5)
- Advanced AI features like project Q&A or sub-task generation (deferred to v1.5/v2)
- Granular AI permission scopes (basic PAT scoping only in v1)

## Target Personas

### Primary Personas

**1. AI-Forward Developer**
- Uses Cursor/Zed with Claude/GPT daily
- Wants to grant their AI assistant programmatic access to project management
- Frustrated by context switching to update tickets
- Values: Speed, automation, focus time, powerful APIs/tooling

**2. Engineering Lead**
- Manages 5-15 developers across multiple projects
- Needs visibility without micromanagement
- Wants AI to quickly summarize progress and streamline reporting
- Values: Real-time status, unblocked teams, predictability

**3. AI Assistant (MCP Client)**
- An external agent, configured by the AI-Forward Developer
- Needs programmatic access to task context via secure endpoint
- Authenticates using user-generated Personal Access Token (PAT)
- Values: Rich context, safe operations, clear feedback, well-defined schemas

### Secondary Personas

**4. Product Manager**
- Collaborates with engineering on priorities
- Wants to leverage AI to distill long discussions into clear summaries
- Values: Visibility, communication, delivery tracking

**5. External Stakeholder**
- Needs to report issues or check status
- Has limited technical knowledge
- Values: Simplicity, responsiveness, clarity

## User Stories & Acceptance Criteria

### Core Platform Stories

**Authentication & Teams**
- As a new user, I can sign up via GitHub/Google OAuth and immediately create a team
- As a team owner, I can invite members via email with role-based permissions
- As a developer, I can generate and revoke Personal Access Tokens (PATs) with defined scopes and expiration dates

**Project Management**
- As a team admin, I can create projects with custom task prefixes (e.g., "AUTH", "API")
- As a project member, I can view all projects I have access to in a single dashboard
- As a project owner, I can configure Git repository connections with appropriate permissions

### Task Management Stories

**Task Creation & Updates**
- As a developer, I can create tasks with markdown descriptions
- As a developer, I can drag tasks between status columns with instant persistence
- As a developer, I can use keyboard shortcuts for all common actions
- As a developer, I can see blocked tasks visually indicated when dependencies aren't met
- As a developer, I can set a parent task that blocks progression to active status

**Task Views**
- As a developer, I can toggle between Kanban board and list view
- As a developer, I can access "My Tasks" showing all my assignments across projects
- As a team member, I can filter tasks by assignee, label, status, or search term
- As a team member, I can see who's currently viewing the same board (presence)

### Collaboration Stories

**Comments & Discussions**
- As a team member, I can add comments to tasks using markdown formatting
- As a team member, I can @mention colleagues to bring them into discussions
- As a mentioned user, I receive immediate notifications (in-app and email)
- As a commenter, I can edit my own comments (with "edited" indicator after 15 minutes)
- As a commenter, I can delete my own comments (shown as "[deleted]")
- As a team member, I see new comments appear in real-time without refreshing
- As a team member, I can see who's currently viewing the same task

**Notifications**
- As a user, I can see an inbox of all notifications relevant to me
- As a user, I receive email notifications for @mentions and assignments
- As a user, I can configure notification preferences per project

### Git Integration Stories

**Branch & PR Management**
- As a developer, I can create a feature branch directly from a task
- As a developer, I can see PR status (draft/open/merged) on task cards
- As a developer, my task automatically updates to "In Review" when I open a PR
- As a developer, my task automatically moves to "Done" when PR is merged
- As a developer, I can see CI/CD status on tasks with linked PRs

### AI Integration Stories

**External Agents (BYOA via MCP)**
- As an AI, I can authenticate to the MCP endpoint using a user-provided PAT
- As an AI, I can list all projects accessible to my authenticated user
- As an AI, I can retrieve tasks with their comments for full context
- As an AI, I can update task status and add PR links
- As an AI, I can create branches and PRs for tasks I'm working on
- As a team admin, I can see which tasks were updated by AI vs humans

**Managed AI Features (In-App)**
- As a team member, I can get an AI-generated summary of a long comment thread
- As a team member, I can get an AI summary of task status and blockers
- As a team lead, I can see AI-generated project health summaries

## Success Metrics

### Activation Metrics
- **Day 1**: 80% of new users connect GitHub and create first task
- **Week 1**: 30% of teams have at least one member generate a PAT
- **Week 1**: 50% of active teams have used AI summarization

### Engagement Metrics
- **MCP Adoption**: ≥20% of task updates via external AI agents in engaged teams
- **Managed AI Adoption**: ≥50% of MAU use AI summarization monthly
- **DAU/MAU**: ≥35% 
- **Task Velocity**: 20% increase in completion rate after 30 days
- **Comment Engagement**: >60% of team members post weekly

### Quality Metrics
- **AI Accuracy**: <5% of AI updates reverted by humans
- **Performance**: P95 page load <400ms, API response <200ms
- **Reliability**: 99.9% uptime for API and MCP endpoints
- **NPS**: >40 after 60 days

### Business Metrics
- **Trial-to-Paid**: 25% conversion rate
- **Target ARR**: $1M in Year 1 from 100 paying teams
- **Churn**: <15% monthly for paid tiers
- **MRR Growth**: 20% month-over-month in first 6 months

## Information Architecture

### Core Entities

**Account**
- Billing boundary, subscription management
- Multiple teams support

**Team**
- Users, projects, shared settings
- Collaboration boundary

**Project**
- Tasks, workflow configuration
- Git repository connections
- Custom task prefixes

**Task**
- Core work unit with status workflow
- parent_task_id for simple blocking (v1)
- Planned: many-to-many dependencies (v1.5)

**Comment**
- Flat, chronological discussions (v1)
- Markdown content with @mentions
- Soft delete support
- Planned: threaded replies (v1.5)

**User**
- Human or AI agent authentication
- Personal Access Token management

**PersonalAccessToken**
- Scoped permissions (read/write)
- Expiration dates (90 days default)
- Usage tracking and rate limiting

**PullRequestLink**
- Minimal cached PR status
- Task associations
- Webhook update timestamps

**Activity**
- Immutable audit log
- Human vs AI action tracking

**Notification**
- User-specific alerts
- @mention routing

### Data Design Principles
- **Git as Source of Truth**: Only cache minimal PR status, never duplicate Git data
- **Real-Time First**: All changes propagate instantly via Phoenix Channels
- **Audit Everything**: Immutable activity log for compliance and analytics
- **AI-Aware Schema**: Explicitly track human vs AI actions
- **Forward Compatibility**: Design for future many-to-many dependencies

## Functional Requirements

### Account & Team Management
- OAuth-based signup/signin (GitHub, Google)
- Multi-team support per user
- Role-based permissions (Owner, Admin, Member, Viewer)
- Billing and subscription management

### Personal Access Token Management
- Token generation with descriptive names
- Scoped permissions (read-only, read-write, project-specific)
- Automatic expiration (configurable, 90 days default)
- Usage analytics and audit logging
- Rate limiting (1000 requests/hour/token)
- Token revocation with immediate effect

### Project Configuration
- Custom task prefixes and sequences
- Configurable workflow columns
- Git repository connections (multiple repos support planned)
- Default assignees and labels

### Task Management
- Rich markdown descriptions
- Simple parent task blocking (v1)
- Multi-assignee support
- Labels, priority, due dates
- Real-time status updates
- Keyboard-driven interface
- Visual blocking indicators

### Task Dependencies
**v1 Implementation (Simple Blocking)**
- Single parent_task_id support
- Parent must be completed before child can activate
- Visual "Blocked by: [TASK-123]" indicator

**v1.5 Planning (Many-to-Many)**
- Architecture designed for future dependency graph
- Service layer abstraction for easy migration
- Planned: multiple dependencies, types, cycle detection

### Comments & Discussions
**v1 Implementation (Flat Comments)**
- Single-level chronological comments
- Full markdown support with preview
- @mentions with autocomplete
- Real-time updates via Phoenix Channels
- Edit/delete own comments
- Presence indicators
- "Edited" timestamp after 15 minutes

**Markdown Features**
- Headers, bold, italic, code blocks
- Links, lists, tables
- Syntax highlighting for code
- XSS prevention via sanitization

### Git Integration
**Core Features**
- OAuth connection to GitHub/GitLab
- One-click branch creation from tasks
- Automatic PR ↔ Task linking
- Webhook processing for real-time updates
- CI/CD status display
- Commit message parsing for task references

**Git Data Strategy**
- Query Git APIs on-demand for details
- Cache only essential status for performance
- Use webhooks to maintain cache freshness

### AI/MCP Interface (External Agents)
**Protocol & Transport**
- Streamable HTTP with SSE support
- JSON-RPC 2.0 protocol
- PAT-based authentication

**Tool Suite**
- `list_projects`: Get accessible projects
- `get_task_with_context`: Retrieve task with comments
- `update_task`: Change status, add PR links
- `create_branch`: Initialize feature branches
- `create_pr`: Open pull requests with context

**Security & Limits**
- Rate limiting per PAT
- Scoped permissions
- Audit log for all operations
- Request/response logging

### Managed AI Features (In-App)
**Task Summarization**
- On-demand summary of task and comments
- Extract key decisions and blockers
- Identify participants and positions

**Comment Thread Summary**
- Activate for threads >10 comments
- Cache summaries for 24 hours
- Show at top of comment section

**Implementation**
- Managed API keys for LLM providers
- Usage quotas per team/tier
- Multiple provider support for redundancy
- Privacy-conscious data handling

### Search & Filtering
- Full-text search across tasks and comments
- Multi-criteria filtering
- Saved views and filters
- Quick filters ("My Tasks", "Needs Review")

### Notifications
**In-App**
- Notification inbox with unread count
- Real-time delivery
- Mark as read/unread

**Email**
- @mention notifications
- Task assignment alerts
- Daily/weekly digests (optional)

### Data Import/Export
- CSV export for all task data
- Basic CSV import for task creation
- API bulk operations support
- Planned: Migration tools from Jira/Asana (v1.5)

## Non-Functional Requirements

### Performance
- **Page Load**: P95 < 400ms
- **API Response**: P95 < 200ms
- **MCP Tool Latency**: P95 < 400ms
- **Comment Post**: < 200ms
- **Real-time Updates**: < 100ms propagation
- **Kanban with 100+ tasks**: < 500ms render

### Scalability
- 10,000 tasks per project
- 100 concurrent users per board
- 1M total tasks per account
- 100+ comments per task without pagination
- 1000 API requests/minute per team

### Security
- OAuth 2.0 for authentication
- Encrypted PATs at rest (AES-256)
- TLS 1.3 for all connections
- Webhook signature validation
- CORS/CSP policies for MCP
- XSS prevention in markdown
- SQL injection protection
- Rate limiting on all endpoints

### Reliability
- 99.9% uptime SLA
- Automatic failover for critical services
- Database backups every 6 hours
- Point-in-time recovery
- Disaster recovery < 4 hours
- Zero-downtime deployments

### Observability
- OpenTelemetry instrumentation
- Structured logging with trace IDs
- Real-time metrics dashboards
- Error tracking and alerting
- AI action audit trail
- Performance monitoring
- Usage analytics

## User Experience Requirements

### Design Principles
- **Keyboard First**: Every action accessible via shortcuts
- **Information Density**: Maximum context without clutter
- **Real-Time Feedback**: Instant updates, optimistic UI
- **Progressive Disclosure**: Advanced features don't overwhelm
- **AI Transparency**: Clear indication of AI actions
- **Markdown Native**: Support markdown everywhere

### Key Interfaces

**Kanban Board**
- Drag-and-drop with smooth animations
- Inline editing of task titles
- Quick actions on hover
- Presence avatars showing viewers
- PR/CI status badges
- Visual blocking indicators
- WIP limit warnings (v1.5)

**Task Detail View**
- Split view with comments
- Markdown editor with preview
- Git integration panel
- Activity timeline
- Dependency visualization
- @mention autocomplete

**Comment Interface**
- Clean, scannable design
- Avatar, name, timestamp per comment
- Markdown rendering with syntax highlighting
- Presence indicators
- Typing indicators
- Smooth real-time updates

**My Tasks View**
- Unified across all projects
- Grouped by status
- Sorted by priority
- Quick status updates
- Bulk operations
- Personal productivity metrics

**Command Palette**
- Global shortcut (Cmd/Ctrl + K)
- Fuzzy search everything
- Quick actions
- Recent items
- Keyboard navigation

### Accessibility
- WCAG 2.1 AA compliance
- Full keyboard navigation
- Screen reader support
- High contrast theme
- Focus indicators
- Reduced motion option

## Technical Architecture

### Core Stack
- **Backend**: Elixir 1.17+, Phoenix 1.7+, Ecto, PostgreSQL 15
- **Real-time**: Phoenix Channels, Presence
- **Frontend**: Phoenix LiveView, TailwindCSS, Alpine.js
- **AI Interface**: MCP server via Streamable HTTP/SSE
- **Git Integration**: GitHub/GitLab APIs, Webhook processing
- **Deployment**: Docker containers, Kubernetes orchestration
- **CDN**: CloudFlare for assets
- **File Storage**: S3-compatible for future attachments

### Key Design Decisions

**LiveView for Interactivity**
- Server-side rendering with real-time updates
- Reduced JavaScript complexity
- Built-in real-time sync

**PostgreSQL Only (v1)**
- Leverage JSONB for flexible data
- Full-text search built-in
- No separate cache initially

**Dependency Abstraction**
```elixir
# Service layer for future-proof dependencies
TaskDependencyService.can_transition?(task, new_status)
# v1: checks parent_task_id
# v1.5: checks dependency graph
```

**Hybrid AI Strategy**
- External agents via MCP for power users
- Managed AI for accessible team features
- Abstract LLM interface for provider flexibility

**Git Integration Approach**
- Minimal status caching for performance
- On-demand API queries for details
- Webhook-driven cache updates

### Security Architecture
- **Authentication**: OAuth 2.0 with PKCE
- **Authorization**: RBAC with team boundaries
- **Encryption**: AES-256 at rest, TLS 1.3 in transit
- **Secrets Management**: HashiCorp Vault or AWS Secrets Manager
- **Audit Logging**: Immutable, tamper-evident logs
- **Rate Limiting**: Token bucket algorithm per endpoint

### Deployment Strategy
- **Blue-Green Deployments**: Zero downtime updates
- **Database Migrations**: Reversible, tested in staging
- **Feature Flags**: Progressive rollout capability
- **Monitoring**: APM, error tracking, uptime monitoring
- **Backup Strategy**: Automated daily backups, 30-day retention

## Pricing Strategy

### Tiers

**Freemium (Free)**
- Up to 3 users, 3 projects
- Core PM features (Kanban, tasks, comments)
- Limited Git integration (read-only status)
- 10 AI summarizations/month

**Team ($12/user/month)**
- Unlimited projects
- Full Git integration
- Full MCP endpoint access (BYOA)
- 100 AI summarizations/user/month
- Email support

**Business ($25/user/month)**
- Everything in Team
- Unlimited AI summarizations
- Advanced analytics
- Priority support
- 99.9% uptime SLA

**Enterprise (Custom)**
- SSO integration
- Dedicated support
- Custom SLA
- BYOK for AI models
- On-premise option

### Overage Pricing
- Additional AI summarizations: $0.10 each
- Bandwidth overage: $10/TB
- Storage overage: $5/GB/month

## Go-to-Market Strategy

### Positioning
"The IDE for Project Management - Where your AI agent, your code, and your tasks converge."

### Target Market
1. **Primary**: AI-forward dev teams (5-50 developers)
2. **Secondary**: Modern engineering teams seeking Git integration
3. **Tertiary**: Agencies building software

### Launch Strategy

**Phase 1: Private Beta (Week 1-4)**
- 10 hand-picked teams
- Direct feedback channels
- Rapid iteration on core features

**Phase 2: Public Beta (Week 5-8)**
- 100 teams from waitlist
- Community feedback forum
- Performance testing at scale

**Phase 3: ProductHunt Launch (Week 9)**
- Coordinated launch campaign
- Influencer outreach
- Early bird pricing

**Phase 4: General Availability (Week 10+)**
- Press release
- Content marketing push
- Conference presentations

### Distribution Channels
- **Product-Led Growth**: Self-serve signup
- **Developer Communities**: HN, Reddit, Dev.to
- **AI Tool Ecosystem**: Cursor, Zed partnerships
- **Content Marketing**: Blog, YouTube tutorials
- **Open Source**: Consider open-core model

## Implementation Milestones

### M0: Foundation (Week 1-2)
- Authentication, team management, RBAC
- Base Phoenix app with LiveView
- PostgreSQL schema with migration strategy
- PAT management system
- Development environment

### M1: Core PM (Week 3-5)
- Projects and Tasks CRUD
- Kanban board with drag-drop
- List view and My Tasks view
- Flat comments with @mentions
- Real-time updates via Channels
- Basic search and filtering

### M2: Git Integration (Week 6-7)
- GitHub/GitLab OAuth
- Branch creation from tasks
- PR status caching
- Webhook processing
- Commit message parsing

### M3: AI/MCP (Week 8-9)
- MCP server implementation
- Core tools suite with PAT auth
- Rate limiting and audit logging
- Managed AI summarization
- Usage quotas and metering

### M4: Polish & Beta (Week 10-11)
- Notification system (in-app + email)
- Performance optimization
- Documentation and API docs
- Beta user onboarding
- Feedback collection system

### M5: Launch Prep (Week 12)
- Load testing and optimization
- Security audit
- Marketing site
- Billing integration
- Launch campaign preparation

### Post-Launch Roadmap (v1.5/v2)
- Many-to-many task dependencies
- Threaded comments
- CLI tool
- Mobile apps
- Advanced AI (project Q&A, sub-task generation)
- External issue portal
- Slack/Discord integration
- Custom workflows
- SOC2 compliance
- Enterprise features

## Risks & Mitigations

### Technical Risks

**Risk**: AI makes incorrect updates
**Mitigation**: Comprehensive audit trail, PAT scoping, rate limiting, clear AI action indicators

**Risk**: GitHub API rate limits hit
**Mitigation**: Efficient caching strategy, webhook-driven updates, exponential backoff, queue management

**Risk**: Managed AI costs spiral
**Mitigation**: Strict per-team quotas, cost-effective models, caching, usage monitoring dashboards

**Risk**: Real-time scalability issues
**Mitigation**: Phoenix proven at scale, horizontal scaling ready, Redis addition if needed

**Risk**: Markdown XSS vulnerabilities
**Mitigation**: Server-side sanitization, allowlist approach, security testing, regular audits

### Market Risks

**Risk**: Developers reject another PM tool
**Mitigation**: Focus on unique BYOA differentiation, developer-first UX, community feedback

**Risk**: Non-devs find limited AI value
**Mitigation**: Strong summarization features, collect feedback for v1.5 priorities

**Risk**: Competition from Linear/Jira AI features
**Mitigation**: Deeper integration, faster innovation, open-source community option

### Business Risks

**Risk**: Low conversion from free to paid
**Mitigation**: Strategic feature gating, clear upgrade value, usage-based triggers

**Risk**: High churn after trial
**Mitigation**: Onboarding optimization, early engagement tracking, proactive support

## Success Criteria for v1 Launch

### Must Have
- ✅ Working AI task updates via MCP with PAT auth
- ✅ GitHub integration with PR tracking
- ✅ Real-time Kanban with presence
- ✅ Flat comments with @mentions and markdown
- ✅ My Tasks view across projects
- ✅ AI summarization for tasks/comments
- ✅ Email notifications for mentions
- ✅ Basic task dependencies (parent blocking)
- ✅ CSV export
- ✅ 99.9% uptime in first month

### Should Have
- ✅ GitLab support
- ✅ Keyboard shortcuts
- ✅ Dark mode
- ✅ Basic analytics dashboard
- ✅ Search across tasks and comments
- ✅ Notification inbox

### Nice to Have
- Time tracking
- Recurring tasks
- Task templates
- Board templates
- Rich text editor option
- File attachments

## Security & Compliance Roadmap

### v1 Security
- OAuth 2.0 authentication
- Encrypted data at rest
- TLS everywhere
- Basic audit logging
- Rate limiting

### v1.5 Security
- 2FA support
- Advanced audit trail
- Bug bounty program
- Penetration testing

### v2 Compliance
- SOC2 Type 1
- GDPR compliance
- HIPAA readiness
- ISO 27001

## Conclusion

This PRD defines an AI-native project management platform that treats AI as a first-class team member while maintaining exceptional developer experience. By combining BYOA via MCP with accessible managed AI features, we create a unique value proposition that serves both power users and entire teams.

The key to success is shipping v1 with rock-solid fundamentals: working AI integration, seamless Git workflows, real-time collaboration, and simple but effective commenting. The architecture is designed for evolution, with clear paths to advanced features like many-to-many dependencies and threaded discussions.

This focused approach creates a new category of developer tool that eliminates context switching, accelerates delivery, and makes AI a natural part of the development workflow.