[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:M0: Foundation - Authentication & Multi-Tenant Architecture DESCRIPTION:Establish the core authentication system and multi-tenant architecture (Account → Team → User) as defined in the PRD. This milestone covers Week 1-2 of the implementation plan.
--[x] NAME:Generate Phoenix authentication system DESCRIPTION:Run phx.gen.auth to create magic link authentication foundation with User schema and authentication flows. Configure the generated authentication system for our multi-tenant architecture.
--[x] NAME:Design and implement multi-tenant database schema DESCRIPTION:Create Account, Team, and User schemas with proper relationships. Account has many Teams, Team has many Users through TeamMembership. Include role-based permissions (Owner, Admin, Member, Viewer).
--[x] NAME:Implement Personal Access Token (PAT) management DESCRIPTION:Create PersonalAccessToken schema with scoped permissions, expiration dates, and usage tracking. Include generation, revocation, and audit logging capabilities for AI agent authentication.
--[x] NAME:Setup role-based access control (RBAC) system DESCRIPTION:Implement role-based access control (RBAC) system using the Bodyguard library. Add Bodyguard dependency and create authorization policies for team roles (Owner, Admin, Member, Viewer) with proper permission checks throughout the application. Create helper functions and plugs for authorization that integrate with our multi-tenant TeamMembership schema.
--[x] NAME:Configure Phoenix LiveView with TailwindCSS DESCRIPTION:Setup Phoenix LiveView for real-time UI, configure TailwindCSS v4 with proper import syntax, and create base layout components following Phoenix v1.8 guidelines.
--[x] NAME:Implement team management and invitation system DESCRIPTION:Create team creation, member invitation via email, and team switching functionality. Include team settings and member management interfaces.
-[ ] NAME:M1: Core Project Management Features DESCRIPTION:Implement the core PM functionality including Projects, Tasks, Kanban boards, and flat comment system. This covers Week 3-5 of the implementation plan.
--[x] NAME:Create Project and Task schemas with relationships DESCRIPTION:Design and implement Project schema with custom task prefixes, and Task schema with status workflow, parent_task_id for simple blocking, and proper associations to Project and User.
--[/] NAME:Build Kanban board with drag-and-drop functionality DESCRIPTION:Create LiveView-based Kanban board with real-time updates via Phoenix Channels, drag-and-drop task movement, and presence indicators showing who's viewing the board.
--[ ] NAME:Implement flat comment system with @mentions DESCRIPTION:Create Comment schema and LiveView interface for flat, chronological comments with markdown support, @mention autocomplete, real-time updates, and edit/delete functionality.
--[ ] NAME:Create My Tasks view and filtering system DESCRIPTION:Build unified My Tasks view across all projects with status grouping, priority sorting, and basic search/filtering capabilities.
-[ ] NAME:M2: Git Integration DESCRIPTION:Add GitHub/GitLab OAuth, branch creation, PR tracking, and webhook processing. This covers Week 6-7 of the implementation plan.
-[ ] NAME:M3: AI/MCP Integration DESCRIPTION:Implement MCP server for external AI agents and managed AI summarization features. This covers Week 8-9 of the implementation plan.
-[ ] NAME:M4: Polish & Beta Preparation DESCRIPTION:Add notifications, performance optimization, documentation, and beta user onboarding. This covers Week 10-11 of the implementation plan.
-[ ] NAME:M5: Launch Preparation DESCRIPTION:Load testing, security audit, marketing site, billing integration, and launch campaign preparation. This covers Week 12 of the implementation plan.
-[x] NAME:Create Project schema with UUID7 primary key DESCRIPTION:Design and implement the Project schema with UUID7 primary key, team association, custom task prefixes, and proper validations. Include fields for name, description, task_prefix, status, and settings.
-[x] NAME:Create Task schema with UUID7 primary key DESCRIPTION:Design and implement the Task schema with UUID7 primary key, project/user associations, parent_task_id for blocking, status workflow, and auto-incrementing task numbers per project.
-[x] NAME:Create migration for projects and tasks tables DESCRIPTION:Generate migration file to create projects and tasks tables with UUID7 primary keys, proper indexes, constraints, and foreign key relationships.
-[x] NAME:Create Projects context module DESCRIPTION:Implement the Projects context module with functions for creating, updating, and querying projects and tasks with proper authorization and multi-tenant filtering.
-[x] NAME:Add tests for Project and Task schemas DESCRIPTION:Write comprehensive tests for the Project and Task schemas, including validations, relationships, and edge cases.
-[x] NAME:Create project board LiveView DESCRIPTION:Create the main LiveView for displaying the Kanban board for a specific project, with proper authentication and team authorization.
-[ ] NAME:Implement Kanban board UI components DESCRIPTION:Create reusable components for Kanban columns, task cards, and drag-and-drop functionality using Phoenix LiveView and JavaScript hooks.
-[ ] NAME:Add drag-and-drop JavaScript hooks DESCRIPTION:Implement client-side JavaScript hooks for drag-and-drop functionality that communicates with the LiveView for task status updates.
-[ ] NAME:Implement real-time updates with Phoenix Channels DESCRIPTION:Set up Phoenix Channels for real-time board updates when tasks are moved or modified by other users.
-[ ] NAME:Add presence indicators DESCRIPTION:Implement Phoenix Presence to show which users are currently viewing the board.
-[x] NAME:Create project navigation and routing DESCRIPTION:Add routes for project boards and integrate with existing team navigation structure.
-[ ] NAME:Add tests for Kanban board functionality DESCRIPTION:Write comprehensive tests for the LiveView, components, and real-time functionality.