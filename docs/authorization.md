# Authorization System (RBAC)

CodeBake implements a comprehensive Role-Based Access Control (RBAC) system using the Bodyguard library. This system provides fine-grained permissions based on team membership and roles.

## Overview

The authorization system is built around our multi-tenant architecture:
- **Account** → **Team** → **User** hierarchy
- Team-based permissions with role hierarchy
- Integration with Phoenix controllers and LiveViews

## Roles and Permissions

### Role Hierarchy

1. **Owner** (Level 4) - Full access to everything in the team
2. **Admin** (Level 3) - Can manage team settings, invite/remove members, manage all projects/tasks
3. **Member** (Level 2) - Can create/edit projects and tasks, comment
4. **Viewer** (Level 1) - Read-only access to projects and tasks

### Permission Matrix

| Action | Viewer | Member | Admin | Owner |
|--------|--------|--------|-------|-------|
| Read team | ✅ | ✅ | ✅ | ✅ |
| Read projects/tasks | ✅ | ✅ | ✅ | ✅ |
| Create projects/tasks | ❌ | ✅ | ✅ | ✅ |
| Update team settings | ❌ | ❌ | ✅ | ✅ |
| Manage members | ❌ | ❌ | ✅ | ✅ |
| Delete team | ❌ | ❌ | ❌ | ✅ |

## Core Components

### 1. Authorization Policy (`Codebake.Authorization`)

The main policy module that implements `Bodyguard.Policy`:

```elixir
# Check authorization
case Bodyguard.permit(Codebake.Authorization, :read_team, current_scope, team) do
  :ok -> # authorized
  {:error, :unauthorized} -> # not authorized
end

# Boolean check
if Codebake.Authorization.can?(current_scope, :update_team, team) do
  # authorized
end
```

### 2. Web Authorization Helpers (`CodebakeWeb.Authorization`)

Convenient helpers for controllers and LiveViews:

```elixir
# In controllers
def show(conn, %{"team_id" => team_id}) do
  with {:ok, team} <- load_and_authorize_team(conn, team_id, :read_team) do
    render(conn, :show, team: team)
  end
end

# In LiveViews
def mount(%{"project_id" => project_id}, _session, socket) do
  case load_and_authorize_project_lv(socket, project_id, :read_project) do
    {:ok, project, socket} ->
      {:ok, assign(socket, :project, project)}
    {:error, socket} ->
      {:ok, socket}
  end
end
```

### 3. Authorization Plug (`CodebakeWeb.Plugs.Authorize`)

Router-level authorization:

```elixir
# In router
pipeline :authorize_project_access do
  plug CodebakeWeb.Plugs.Authorize, action: :read_project, resource: :project
end

scope "/projects/:project_id", CodebakeWeb do
  pipe_through [:browser, :require_authenticated_user, :authorize_project_access]

  live "/", ProjectLive.Board, :show
  live "/settings", ProjectLive.Settings, :edit
end
```

## Usage Examples

### Controller Authorization

```elixir
defmodule MyController do
  use CodebakeWeb, :controller
  import CodebakeWeb.Authorization
  
  def show(conn, %{"project_id" => project_id}) do
    with {:ok, project} <- load_and_authorize_project(conn, project_id, :read_project) do
      render(conn, :show, project: project)
    end
  end

  def update(conn, %{"project_id" => project_id, "project" => params}) do
    with {:ok, project} <- load_and_authorize_project(conn, project_id, :update_project),
         {:ok, updated_project} <- Projects.update_project(project, params) do
      render(conn, :show, project: updated_project)
    end
  end
end
```

### LiveView Authorization

```elixir
defmodule MyLive do
  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization
  
  def mount(%{"project_id" => project_id}, _session, socket) do
    case load_and_authorize_project_lv(socket, project_id, :read_project) do
      {:ok, project, socket} ->
        socket =
          socket
          |> assign(:project, project)
          |> assign(:can_manage, can_manage_project?(socket, project))

        {:ok, socket}

      {:error, socket} ->
        {:ok, socket}
    end
  end

  def handle_event("update_project", params, socket) do
    if can?(socket, :update_project, socket.assigns.project) do
      # Handle update
    else
      socket = put_flash(socket, :error, "Unauthorized")
      {:noreply, socket}
    end
  end
end
```

### Template Authorization

```heex
<%= if can?(assigns, :update_project, @project) do %>
  <button>Edit Project</button>
<% end %>

<%= if team_admin?(assigns, @team) do %>
  <button>Manage Members</button>
<% end %>

<%= if team_owner?(assigns, @team) do %>
  <button class="text-red-600">Delete Team</button>
<% end %>
```

## Helper Functions

### Permission Checking

- `can?(scope, action, resource)` - Check if user can perform action
- `has_min_role?(scope, team, min_role)` - Check minimum role level
- `user_role_in_team(scope, team)` - Get user's role in team

### Role Convenience Functions

- `team_owner?(scope, team)` - Check if user is team owner
- `team_admin?(scope, team)` - Check if user is admin or owner
- `can_manage_team?(scope, team)` - Check if user can manage team
- `can_contribute?(scope, team)` - Check if user can contribute

### User Information

- `current_user(scope)` - Get current user
- `authenticated?(scope)` - Check if user is authenticated

## Available Actions

### Team-level Actions

- `:read_team` - View team information
- `:update_team` - Modify team settings
- `:delete_team` - Delete the team
- `:manage_members` - Invite/remove team members
- `:manage_settings` - Change team settings

### Project-level Actions (Future)

- `:read_projects` - View projects
- `:create_project` - Create new projects
- `:update_project` - Modify projects
- `:delete_project` - Delete projects

### Task-level Actions (Future)

- `:read_tasks` - View tasks
- `:create_task` - Create new tasks
- `:update_task` - Modify tasks
- `:delete_task` - Delete tasks

## Error Handling

The authorization system provides consistent error handling:

- **Controllers**: Returns JSON error responses with appropriate HTTP status codes
- **LiveViews**: Redirects with flash messages
- **Plugs**: Halts request with error response

## Testing

Use the provided test helpers for authorization testing:

```elixir
test "team authorization", %{team: team, user: user} do
  scope = Scope.for_user(user)
  
  assert Authorization.can?(scope, :read_team, team)
  refute Authorization.can?(scope, :delete_team, team)
end
```

## Best Practices

1. **Always authorize at the boundary** - Check permissions in controllers/LiveViews, not in business logic
2. **Use helper functions** - Prefer `load_and_authorize_team/3` over manual checks
3. **Fail securely** - Default to denying access when in doubt
4. **Test permissions** - Write tests for all authorization scenarios
5. **Use plugs for common patterns** - Leverage router-level authorization when appropriate

## Integration with Existing Code

The RBAC system integrates seamlessly with the existing authentication system:

- Uses existing `current_scope` assigns
- Works with existing `TeamMembership` schema
- Respects existing role definitions
- Compatible with Personal Access Token authentication
