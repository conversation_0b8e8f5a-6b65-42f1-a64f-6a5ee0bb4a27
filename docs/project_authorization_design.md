# Project-Level Authorization Design

## Overview

This document outlines the new project-level authorization system that replaces the Team-based system. Users will have direct relationships with Projects through ProjectMembership records.

## Schema Changes

### New ProjectMembership Schema

```elixir
schema "project_memberships" do
  field :role, :string, default: "member"
  field :invited_at, :utc_datetime
  field :joined_at, :utc_datetime

  belongs_to :project, Codebake.Projects.Project
  belongs_to :user, Codebake.Accounts.User, type: :id
  belongs_to :invited_by, Codebake.Accounts.User, type: :id

  timestamps(type: :utc_datetime)
end
```

### Updated Project Schema

```elixir
schema "projects" do
  # Remove team_id dependency
  # belongs_to :team, Codebake.Accounts.Team, type: :binary_id  # REMOVE THIS
  
  # Add direct account relationship
  belongs_to :account, Codebake.Accounts.Account, type: :binary_id
  
  # Add project memberships
  has_many :project_memberships, Codebake.Projects.ProjectMembership
  has_many :users, through: [:project_memberships, :user]
  
  # ... other fields remain the same
end
```

## Authorization Functions

### Core Authorization Functions (to add to Authorization module)

```elixir
# Project-level authorization functions
defp do_authorize(:read_project, user, %Project{} = project) do
  if project_member?(user, project), do: :ok, else: {:error, :unauthorized}
end

defp do_authorize(:update_project, user, %Project{} = project) do
  if has_project_role?(user, project, ["member", "admin", "owner"]), 
    do: :ok, else: {:error, :unauthorized}
end

defp do_authorize(:manage_project_members, user, %Project{} = project) do
  if has_project_role?(user, project, ["admin", "owner"]), 
    do: :ok, else: {:error, :unauthorized}
end

defp do_authorize(:delete_project, user, %Project{} = project) do
  if has_project_role?(user, project, ["owner"]), 
    do: :ok, else: {:error, :unauthorized}
end

# Task-level authorization (tasks belong to projects)
defp do_authorize(:read_tasks, user, %Project{} = project) do
  if project_member?(user, project), do: :ok, else: {:error, :unauthorized}
end

defp do_authorize(:create_task, user, %Project{} = project) do
  if has_project_role?(user, project, ["member", "admin", "owner"]), 
    do: :ok, else: {:error, :unauthorized}
end

defp do_authorize(:update_task, user, %Task{} = task) do
  project = Projects.get_project_for_task(task)
  if has_project_role?(user, project, ["member", "admin", "owner"]), 
    do: :ok, else: {:error, :unauthorized}
end
```

### Helper Functions

```elixir
# Helper functions
defp project_member?(%User{} = user, %Project{} = project) do
  Projects.project_member?(user, project)
end

defp has_project_role?(%User{} = user, %Project{} = project, roles) when is_list(roles) do
  case Projects.get_user_role_in_project(user, project) do
    nil -> false
    user_role -> user_role in roles
  end
end
```

## Projects Context Functions

### New Functions to Add to Projects Context

```elixir
@doc """
Check if user is a member of the given project.
"""
def project_member?(%User{} = user, %Project{} = project) do
  from(pm in ProjectMembership,
    where: pm.user_id == ^user.id and pm.project_id == ^project.id
  )
  |> Repo.exists?()
end

@doc """
Get user's role in a project.
"""
def get_user_role_in_project(%User{} = user, %Project{} = project) do
  from(pm in ProjectMembership,
    where: pm.user_id == ^user.id and pm.project_id == ^project.id,
    select: pm.role
  )
  |> Repo.one()
end

@doc """
List all projects for a user across all accounts.
"""
def list_user_projects(%User{} = user, opts \\ []) do
  query =
    from p in Project,
      join: pm in ProjectMembership,
      on: pm.project_id == p.id,
      where: pm.user_id == ^user.id,
      preload: [:account],
      order_by: [desc: p.inserted_at]

  query
  |> maybe_filter_by_account(opts[:account_id])
  |> maybe_filter_by_status(opts[:status])
  |> Repo.all()
end

@doc """
List projects for an account with user's role information.
"""
def list_account_projects_with_user_roles(%Account{} = account, %User{} = user) do
  from(p in Project,
    left_join: pm in ProjectMembership,
    on: pm.project_id == p.id and pm.user_id == ^user.id,
    where: p.account_id == ^account.id,
    select: %{
      project: p,
      user_role: pm.role,
      is_member: not is_nil(pm.id)
    },
    order_by: [desc: p.inserted_at]
  )
  |> Repo.all()
end
```

## Dashboard Changes

### Account Dashboard

The account dashboard will now show projects directly instead of teams:

```elixir
def mount(%{"account_slug" => account_slug}, _session, socket) do
  case load_and_authorize_account_lv(socket, account_slug, :read_account) do
    {:ok, account, socket} ->
      user = current_user(socket)
      projects_with_roles = Projects.list_account_projects_with_user_roles(account, user)
      stats = get_account_project_stats(account, user)

      socket =
        socket
        |> assign(:account, account)
        |> assign(:projects_with_roles, projects_with_roles)
        |> assign(:stats, stats)

      {:ok, socket}
  end
end
```

## Migration Strategy

1. Create `project_memberships` table
2. Add `account_id` to `projects` table
3. Migrate existing team memberships to project memberships
4. Update all authorization code
5. Remove team-related code

## Benefits

1. **Simplified Architecture**: Direct user-to-project relationships
2. **Better Performance**: No need to query through teams
3. **Flexible Access Control**: Users can have different roles on different projects
4. **Easier Client Dashboard**: Direct project access without team navigation
5. **Reduced Complexity**: Fewer entities to manage
