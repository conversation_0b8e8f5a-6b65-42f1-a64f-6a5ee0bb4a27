# This file is responsible for configuring your application
# and its dependencies with the aid of the Config module.
#
# This configuration file is loaded before any dependency and
# is restricted to this project.

# General application configuration
import Config

config :codebake, :scopes,
  user: [
    default: true,
    module: Codebake.Accounts.Scope,
    assign_key: :current_scope,
    access_path: [:user, :id],
    schema_key: :user_id,
    schema_type: :id,
    schema_table: :users,
    test_data_fixture: Codebake.AccountsFixtures,
    test_setup_helper: :register_and_log_in_user
  ]

config :codebake,
  ecto_repos: [Codebake.Repo],
  generators: [timestamp_type: :utc_datetime],
  env: Mix.env(),
  # Environment-specific settings
  audit_logging_enabled: true

# Configures the endpoint
config :codebake, CodebakeWeb.Endpoint,
  url: [host: "localhost"],
  adapter: Bandit.PhoenixAdapter,
  render_errors: [
    formats: [html: CodebakeWeb.ErrorHTML, json: CodebakeWeb.ErrorJSON],
    layout: false
  ],
  pubsub_server: Codebake.PubSub,
  live_view: [signing_salt: "r8LDjrIa"]

# Configures the mailer
#
# By default it uses the "Local" adapter which stores the emails
# locally. You can see the emails in your browser, at "/dev/mailbox".
#
# For production it's recommended to configure a different adapter
# at the `config/runtime.exs`.
config :codebake, Codebake.Mailer, adapter: Swoosh.Adapters.Local

# Configure esbuild (the version is required)
config :esbuild,
  version: "0.25.4",
  codebake: [
    args:
      ~w(js/app.js --bundle --target=es2022 --outdir=../priv/static/assets/js --external:/fonts/* --external:/images/* --alias:@=.),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => [Path.expand("../deps", __DIR__), Mix.Project.build_path()]}
  ]

# Configure tailwind (the version is required)
config :tailwind,
  version: "4.1.7",
  codebake: [
    args: ~w(
      --input=assets/css/app.css
      --output=priv/static/assets/css/app.css
    ),
    cd: Path.expand("..", __DIR__)
  ]

# Configures Elixir's Logger
config :logger, :default_formatter,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# Use Jason for JSON parsing in Phoenix
config :phoenix, :json_library, Jason

# Markdown processing configuration
config :codebake, :markdown,
  # Maximum content length for markdown processing
  max_content_length: 50_000,
  # Enable GitHub Flavored Markdown features
  enable_gfm: true,
  # Enable table support
  enable_tables: true,
  # Enable strikethrough support
  enable_strikethrough: true,
  # Cache processed content (for future implementation)
  enable_caching: false

# S3 Configuration
config :ex_aws,
  access_key_id: System.get_env("AWS_ACCESS_KEY_ID", "test"),
  secret_access_key: System.get_env("AWS_SECRET_ACCESS_KEY", "test"),
  region: System.get_env("AWS_REGION", "us-east-1")

config :ex_aws, :s3,
  scheme: System.get_env("AWS_S3_SCHEME", "https://"),
  host: System.get_env("AWS_S3_HOST", "s3.amazonaws.com"),
  port: System.get_env("AWS_S3_PORT", "443") |> String.to_integer(),
  bucket: System.get_env("S3_BUCKET", "codebake-attachments")

# Import environment specific config. This must remain at the bottom
# of this file so it overrides the configuration defined above.
import_config "#{config_env()}.exs"
