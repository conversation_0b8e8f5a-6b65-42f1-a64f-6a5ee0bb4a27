import Config

# Configure your database
# Prefer DATABASE_URL if provided (e.g., in Docker), otherwise fall back to per-field env vars.
database_url = System.get_env("DATABASE_URL")

db_username = System.get_env("DB_USERNAME", "postgres")
db_password = System.get_env("DB_PASSWORD", "postgres")
db_hostname = System.get_env("DB_HOSTNAME", "localhost")
db_database = System.get_env("DB_DATABASE", "codebake_dev")
pool_size = String.to_integer(System.get_env("POOL_SIZE") || "10")

repo_config =
  if database_url do
    [url: database_url]
  else
    [
      username: db_username,
      password: db_password,
      hostname: db_hostname,
      database: db_database
    ]
  end

config :codebake,
       Codebake.Repo,
       Keyword.merge(repo_config,
         stacktrace: true,
         show_sensitive_data_on_connection_error: true,
         pool_size: pool_size
       )

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
ip = if System.get_env("DOCKER") in ["true", "1"], do: {0, 0, 0, 0}, else: {127, 0, 0, 1}

config :codebake, CodebakeWeb.Endpoint,
  # Bind to all interfaces when running in Docker so host can connect.
  http: [ip: ip, port: String.to_integer(System.get_env("PORT") || "4000")],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "5nzGOph/bBhfs8xeAXNgMU6hezbZPqHDp8XJnFiPyV3KFjBWVwGwoIhi2oZ7b3Be",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:codebake, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:codebake, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Configure your database
# Prefer DATABASE_URL when present (e.g., in Docker),
# otherwise fall back to discrete settings for local dev.
db_url = System.get_env("DATABASE_URL")

if db_url do
  config :codebake, Codebake.Repo,
    url: db_url,
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: String.to_integer(System.get_env("DB_POOL_SIZE") || "10")
else
  config :codebake, Codebake.Repo,
    username: System.get_env("DB_USERNAME") || "postgres",
    password: System.get_env("DB_PASSWORD") || "postgres",
    hostname: System.get_env("DB_HOSTNAME") || "localhost",
    database: System.get_env("DB_DATABASE") || "codebake_dev",
    stacktrace: true,
    show_sensitive_data_on_connection_error: true,
    pool_size: String.to_integer(System.get_env("DB_POOL_SIZE") || "10")
end

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
config :codebake, CodebakeWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [
    ip: {0, 0, 0, 0},
    port: String.to_integer(System.get_env("PORT") || "4000")
  ],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base:
    System.get_env("SECRET_KEY_BASE") ||
      "VEx5C7yTUCXAxcmBbwYv78grg7W8KAubhILcUD8T+80NRcxvvvYlPqsYK6Yt/zu7",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:codebake, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:codebake, ~w(--watch)]}
  ]

# Watch static and templates for browser reloading.
config :codebake, CodebakeWeb.Endpoint,
  live_reload: [
    web_console_logger: true,
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/codebake_web/(?:controllers|live|components|router)/?.*\.(ex|heex)$"
    ]
  ]

# Enable dev routes for dashboard and mailbox
config :codebake, dev_routes: true

# Do not include metadata nor timestamps in development logs
config :logger, :default_formatter, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include debug annotations and locations in rendered markup.
  # Changing this configuration will require mix clean and a full recompile.
  debug_heex_annotations: true,
  debug_attributes: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Disable swoosh api client as it is only required for production adapters.
config :swoosh, :api_client, false

# LocalStack S3 configuration for development
# Use environment variables to support both local development and Docker
s3_host = System.get_env("S3_HOST", "localhost")
s3_port = System.get_env("S3_PORT", "14566") |> String.to_integer()

config :ex_aws, :s3,
  scheme: "http://",
  host: s3_host,
  port: s3_port,
  bucket: "codebake-attachments-dev"

config :phoenix_live_view,
  debug_heex_annotations: true,
  debug_attributes: true
