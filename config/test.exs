import Config

# Only in tests, remove the complexity from the password hashing algorithm
config :bcrypt_elixir, :log_rounds, 1

# Configure your database
#
# The MIX_TEST_PARTITION environment variable can be used
# to provide built-in test partitioning in CI environment.
# Run `mix help test` for more information.
# Prefer DATABASE_URL if provided (e.g., in Docker test service), else fall back to per-field env vars.
database_url = System.get_env("DATABASE_URL")

# Support overriding host when running in Docker (postgres-test) vs local (localhost)
db_hostname = System.get_env("TEST_DB_HOSTNAME", System.get_env("DB_HOSTNAME", "localhost"))
db_username = System.get_env("TEST_DB_USERNAME", System.get_env("DB_USERNAME", "postgres"))
db_password = System.get_env("TEST_DB_PASSWORD", System.get_env("DB_PASSWORD", "postgres"))

db_database =
  System.get_env("TEST_DB_DATABASE", "codebake_test") <>
    (System.get_env("MIX_TEST_PARTITION") || "")

repo_config =
  if database_url do
    [url: database_url]
  else
    [
      username: db_username,
      password: db_password,
      hostname: db_hostname,
      database: db_database
    ]
  end

config :codebake,
       Codebake.Repo,
       Keyword.merge(repo_config,
         pool: Ecto.Adapters.SQL.Sandbox,
         pool_size: System.schedulers_online() * 2,
         ssl: false
       )

# We don't run a server during test. If one is required,
# you can enable the server option below.
config :codebake, CodebakeWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "6jQDDWJwupjjpyl+ISFnQWh+WesACCkGb2jSaOvsidnbei+qWAVO9thSnWVLDKMo",
  server: false

# In test we don't send emails
config :codebake, Codebake.Mailer, adapter: Swoosh.Adapters.Test

# Disable swoosh api client as it is only required for production adapters
config :swoosh, :api_client, false

# Print only warnings and errors during test
config :logger, level: :warning

# Initialize plugs at runtime for faster test compilation
config :phoenix, :plug_init_mode, :runtime

# Enable helpful, but potentially expensive runtime checks
config :phoenix_live_view,
  enable_expensive_runtime_checks: true
