# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     Codebake.Repo.insert!(%Codebake.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

# For development, we can use the Release module directly
# This ensures consistency between development and production seeding
case Codebake.Release.seed() do
  :ok ->
    IO.puts("Seeds completed successfully!")

  {:error, errors} ->
    IO.puts("Seeds failed with errors:")

    Enum.each(errors, fn {:error, module, error} ->
      IO.puts("  - #{module}: #{inspect(error)}")
    end)

    exit({:shutdown, 1})
end
