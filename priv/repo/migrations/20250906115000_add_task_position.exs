defmodule Codebake.Repo.Migrations.AddTaskPosition do
  use Ecto.Migration

  def up do
    alter table(:tasks) do
      add :position, :integer, null: false, default: 0
    end

    create index(:tasks, [:project_id, :status, :position])

    execute("""
      UPDATE tasks t
      SET position = s.rn
      FROM (
        SELECT id, ROW_NUMBER() OVER (PARTITION BY project_id, status ORDER BY task_number) AS rn
        FROM tasks
      ) AS s
      WHERE s.id = t.id
    """)
  end

  def down do
    drop_if_exists index(:tasks, [:project_id, :status, :position])

    alter table(:tasks) do
      remove :position
    end
  end
end
