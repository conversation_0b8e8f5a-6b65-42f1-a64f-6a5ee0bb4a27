defmodule Codebake.Repo.Migrations.AddColorToTeams do
  use Ecto.Migration

  def change do
    alter table(:teams) do
      add :color, :string, default: "blue", null: false
    end

    # Add a check constraint for valid colors
    create constraint(:teams, :valid_color,
             check:
               "color IN ('blue', 'green', 'purple', 'red', 'orange', 'yellow', 'pink', 'indigo', 'teal', 'gray')"
           )
  end
end
