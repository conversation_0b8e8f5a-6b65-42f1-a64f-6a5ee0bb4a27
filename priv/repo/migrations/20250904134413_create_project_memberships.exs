defmodule Codebake.Repo.Migrations.CreateProjectMemberships do
  use Ecto.Migration

  def change do
    # Create project_memberships table
    create table(:project_memberships, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :role, :string, null: false, default: "member"
      add :invited_at, :utc_datetime
      add :joined_at, :utc_datetime

      add :project_id, references(:projects, type: :binary_id, on_delete: :delete_all),
        null: false

      add :user_id, references(:users, type: :id, on_delete: :delete_all), null: false
      add :invited_by_id, references(:users, type: :id, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    # Create indexes for project_memberships
    create unique_index(:project_memberships, [:project_id, :user_id])
    create index(:project_memberships, [:user_id])
    create index(:project_memberships, [:project_id])
    create index(:project_memberships, [:role])

    # Add check constraint for valid roles
    create constraint(:project_memberships, :valid_role,
             check: "role IN ('owner', 'admin', 'member', 'viewer')"
           )

    # Add account_id to projects table to remove team dependency
    alter table(:projects) do
      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all)
    end

    create index(:projects, [:account_id])
  end
end
