defmodule Codebake.Repo.Migrations.CreateProjectsAndTasks do
  use Ecto.Migration

  def change do
    # Create projects table
    create table(:projects, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :team_id, references(:teams, type: :binary_id, on_delete: :delete_all), null: false
      add :created_by_id, references(:users, on_delete: :nilify_all), null: false

      add :name, :string, null: false
      add :description, :text
      add :task_prefix, :string, null: false, default: "TASK"
      add :status, :string, null: false, default: "planning"
      add :task_counter, :integer, null: false, default: 0
      add :settings, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    # Create tasks table
    create table(:tasks, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :project_id, references(:projects, type: :binary_id, on_delete: :delete_all),
        null: false

      add :assignee_id, references(:users, on_delete: :nilify_all)
      add :created_by_id, references(:users, on_delete: :nilify_all), null: false
      add :parent_task_id, references(:tasks, type: :binary_id, on_delete: :nilify_all)

      add :title, :string, null: false
      add :description, :text
      add :status, :string, null: false, default: "todo"
      add :priority, :string, null: false, default: "medium"
      add :task_number, :integer, null: false
      add :task_identifier, :string, null: false
      add :due_date, :utc_datetime
      add :completed_at, :utc_datetime
      add :estimated_hours, :decimal, precision: 8, scale: 2
      add :actual_hours, :decimal, precision: 8, scale: 2
      add :tags, {:array, :string}, default: []
      add :metadata, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    # Create indexes for projects
    create unique_index(:projects, [:team_id, :name])
    create unique_index(:projects, [:team_id, :task_prefix])
    create index(:projects, [:team_id])
    create index(:projects, [:status])
    create index(:projects, [:created_by_id])

    # Create indexes for tasks
    create unique_index(:tasks, [:project_id, :task_number])
    create unique_index(:tasks, [:project_id, :task_identifier])
    create index(:tasks, [:project_id])
    create index(:tasks, [:assignee_id])
    create index(:tasks, [:created_by_id])
    create index(:tasks, [:parent_task_id])
    create index(:tasks, [:status])
    create index(:tasks, [:priority])
    create index(:tasks, [:due_date])
    create index(:tasks, [:completed_at])

    # Create check constraints
    create constraint(:projects, :valid_status,
             check: "status IN ('planning', 'active', 'on_hold', 'completed', 'archived')"
           )

    create constraint(:tasks, :valid_status,
             check: "status IN ('todo', 'in_progress', 'blocked', 'done')"
           )

    create constraint(:tasks, :valid_priority,
             check: "priority IN ('low', 'medium', 'high', 'urgent')"
           )

    create constraint(:tasks, :positive_estimated_hours,
             check: "estimated_hours IS NULL OR estimated_hours > 0"
           )

    create constraint(:tasks, :positive_actual_hours,
             check: "actual_hours IS NULL OR actual_hours > 0"
           )

    create constraint(:tasks, :no_self_parent, check: "parent_task_id != id")
  end
end
