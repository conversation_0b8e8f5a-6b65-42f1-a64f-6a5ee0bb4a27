defmodule Codebake.Repo.Migrations.CreateAccountMemberships do
  use Ecto.Migration

  def change do
    create_if_not_exists table(:account_memberships, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all),
        null: false

      add :user_id, references(:users, type: :id, on_delete: :delete_all), null: false

      add :role, :string, null: false, default: "member"

      timestamps(type: :utc_datetime)
    end

    create_if_not_exists unique_index(:account_memberships, [:account_id, :user_id])
  end
end
