defmodule Codebake.Repo.Migrations.CreatePersonalAccessTokens do
  use Ecto.Migration

  def change do
    create table(:personal_access_tokens, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :name, :string, null: false
      add :token_hash, :string, null: false
      add :scopes, {:array, :string}, default: []
      add :expires_at, :utc_datetime
      add :last_used_at, :utc_datetime
      add :usage_count, :integer, default: 0
      add :ip_whitelist, {:array, :string}, default: []
      add :revoked_at, :utc_datetime
      add :revoked_by_id, references(:users, on_delete: :nilify_all)
      add :metadata, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create unique_index(:personal_access_tokens, [:token_hash])
    create index(:personal_access_tokens, [:user_id])
    create index(:personal_access_tokens, [:expires_at])
    create index(:personal_access_tokens, [:revoked_at])
    create index(:personal_access_tokens, [:last_used_at])
    create index(:personal_access_tokens, [:scopes], using: :gin)

    # Create audit log table for PAT usage
    create table(:personal_access_token_logs, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :personal_access_token_id,
          references(:personal_access_tokens, type: :binary_id, on_delete: :delete_all),
          null: false

      add :action, :string, null: false
      add :ip_address, :string
      add :user_agent, :string
      add :endpoint, :string
      add :method, :string
      add :status_code, :integer
      add :response_time_ms, :integer
      add :request_id, :string
      add :metadata, :map, default: %{}

      timestamps(type: :utc_datetime, updated_at: false)
    end

    create index(:personal_access_token_logs, [:personal_access_token_id])
    create index(:personal_access_token_logs, [:action])
    create index(:personal_access_token_logs, [:inserted_at])
    create index(:personal_access_token_logs, [:ip_address])

    # Add constraints
    create constraint(:personal_access_tokens, :valid_scopes,
             check: "array_length(scopes, 1) > 0 OR scopes = '{}'"
           )
  end
end
