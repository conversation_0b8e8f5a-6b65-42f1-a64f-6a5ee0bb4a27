defmodule Codebake.Repo.Migrations.FixProjectUniqueConstraints do
  use Ecto.Migration

  def up do
    # Drop the old unique constraints that reference the removed team_id column
    drop_if_exists unique_index(:projects, [:team_id, :name])
    drop_if_exists unique_index(:projects, [:team_id, :task_prefix])

    # Create new unique constraints based on account_id
    create unique_index(:projects, [:account_id, :name])
    create unique_index(:projects, [:account_id, :task_prefix])
  end

  def down do
    # Remove the account-based constraints
    drop unique_index(:projects, [:account_id, :name])
    drop unique_index(:projects, [:account_id, :task_prefix])

    # Note: We can't recreate the team_id constraints because team_id column no longer exists
  end
end
