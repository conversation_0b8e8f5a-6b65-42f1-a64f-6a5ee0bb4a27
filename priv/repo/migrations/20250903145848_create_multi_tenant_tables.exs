defmodule Codebake.Repo.Migrations.CreateMultiTenantTables do
  use Ecto.Migration

  def change do
    # Create accounts table (billing boundary)
    create table(:accounts, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :slug, :string, null: false
      add :billing_email, :string
      add :subscription_status, :string, default: "trial"
      add :subscription_tier, :string, default: "freemium"
      add :trial_ends_at, :utc_datetime
      add :settings, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create unique_index(:accounts, [:slug])
    create index(:accounts, [:subscription_status])

    # Create teams table (collaboration boundary)
    create table(:teams, primary_key: false) do
      add :id, :binary_id, primary_key: true

      add :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all),
        null: false

      add :name, :string, null: false
      add :slug, :string, null: false
      add :description, :text
      add :settings, :map, default: %{}

      timestamps(type: :utc_datetime)
    end

    create unique_index(:teams, [:account_id, :slug])
    create index(:teams, [:account_id])

    # Create team_memberships table (many-to-many with roles)
    create table(:team_memberships, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :team_id, references(:teams, type: :binary_id, on_delete: :delete_all), null: false
      add :user_id, references(:users, on_delete: :delete_all), null: false
      add :role, :string, null: false, default: "member"
      add :invited_at, :utc_datetime
      add :joined_at, :utc_datetime
      add :invited_by_id, references(:users, on_delete: :nilify_all)

      timestamps(type: :utc_datetime)
    end

    create unique_index(:team_memberships, [:team_id, :user_id])
    create index(:team_memberships, [:user_id])
    create index(:team_memberships, [:team_id])
    create index(:team_memberships, [:role])

    # Add account_id to users for default account association
    alter table(:users) do
      add :account_id, references(:accounts, type: :binary_id, on_delete: :nilify_all)
      add :name, :string
      add :avatar_url, :string
      add :timezone, :string, default: "UTC"
      add :settings, :map, default: %{}
    end

    create index(:users, [:account_id])

    # Create check constraints for valid roles
    create constraint(:team_memberships, :valid_role,
             check: "role IN ('owner', 'admin', 'member', 'viewer')"
           )
  end
end
