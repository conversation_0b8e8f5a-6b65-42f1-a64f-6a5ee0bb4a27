defmodule Codebake.Repo.Migrations.MigrateTeamsToProjects do
  use Ecto.Migration
  import Ecto.Query

  def up do
    # This migration transforms the team-based system to a project-based system
    # 1. Update projects to reference accounts directly instead of teams
    # 2. Create project memberships for all users who were team members
    # 3. Preserve user roles from team memberships

    # First, update all projects to reference their team's account
    execute """
    UPDATE projects
    SET account_id = teams.account_id
    FROM teams
    WHERE projects.team_id = teams.id
    """

    # Create project memberships for all team members across all projects in their teams
    execute """
    INSERT INTO project_memberships (id, project_id, user_id, role, invited_at, joined_at, invited_by_id, inserted_at, updated_at)
    SELECT
      gen_random_uuid() as id,
      p.id as project_id,
      tm.user_id,
      tm.role,
      tm.invited_at,
      tm.joined_at,
      tm.invited_by_id,
      NOW() as inserted_at,
      NOW() as updated_at
    FROM projects p
    JOIN teams t ON p.team_id = t.id
    JOIN team_memberships tm ON tm.team_id = t.id
    WHERE tm.joined_at IS NOT NULL  -- Only include users who have actually joined
    """

    # Make account_id NOT NULL now that all projects have been updated
    # (The foreign key constraint already exists from the previous migration)
    alter table(:projects) do
      modify :account_id, :binary_id, null: false
    end

    # Remove the team_id foreign key constraint and column
    alter table(:projects) do
      remove :team_id
    end
  end

  def down do
    # This is a destructive migration - we cannot fully reverse it
    # because we're removing the team structure entirely

    # Re-add team_id column
    alter table(:projects) do
      add :team_id, references(:teams, type: :binary_id, on_delete: :delete_all)
    end

    # Make account_id nullable again
    alter table(:projects) do
      modify :account_id, references(:accounts, type: :binary_id, on_delete: :delete_all),
        null: true
    end

    # Note: We cannot restore the original team_id values or team memberships
    # This would require manual intervention or a backup
    raise "This migration cannot be automatically reversed. Manual intervention required."
  end
end
