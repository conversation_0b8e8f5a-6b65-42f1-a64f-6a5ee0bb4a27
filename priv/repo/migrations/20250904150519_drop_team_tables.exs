defmodule Codebake.Repo.Migrations.DropTeamTables do
  use Ecto.Migration

  def up do
    # Drop team_memberships table first (has foreign key to teams)
    drop table(:team_memberships)

    # Then drop teams table
    drop table(:teams)
  end

  def down do
    # This migration cannot be reversed as it permanently deletes data
    # The team tables and all their data would be lost
    raise "Cannot reverse dropping team tables - this would result in permanent data loss"
  end
end
