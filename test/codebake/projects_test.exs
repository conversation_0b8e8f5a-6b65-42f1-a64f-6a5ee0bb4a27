defmodule Codebake.ProjectsTest do
  use Codebake.DataCase

  import Codebake.AccountsFixtures
  alias <PERSON>bake.Projects
  alias Codebake.Projects.{Project, Task}

  describe "projects" do
    setup do
      account = account_fixture()
      user = user_fixture(%{account_id: account.id})

      %{account: account, user: user}
    end

    test "list_account_projects/1 returns all projects for an account", %{
      account: account,
      user: user
    } do
      project1 = project_fixture(account, user)
      project2 = project_fixture(account, user)

      projects = Projects.list_account_projects(account)
      assert length(projects) == 2
      assert Enum.any?(projects, &(&1.id == project1.id))
      assert Enum.any?(projects, &(&1.id == project2.id))
    end

    test "get_project/1 returns the project with given id", %{account: account, user: user} do
      project = project_fixture(account, user)
      assert Projects.get_project(project.id) == project
    end

    test "get_project/1 returns nil for non-existent project", %{account: _account, user: _user} do
      assert Projects.get_project(Ecto.UUID.generate()) == nil
    end

    test "create_project/3 with valid data creates a project", %{account: account, user: user} do
      valid_attrs = %{
        "name" => "Test Project",
        "description" => "A test project",
        "task_prefix" => "TEST"
      }

      assert {:ok, %Project{} = project} = Projects.create_project(account, user, valid_attrs)
      assert project.name == "Test Project"
      assert project.description == "A test project"
      assert project.task_prefix == "TEST"
      assert project.account_id == account.id
      assert project.created_by_id == user.id
      assert project.status == "planning"
      assert project.task_counter == 0
    end

    test "create_project/3 with invalid data returns error changeset", %{
      account: account,
      user: user
    } do
      assert {:error, %Ecto.Changeset{}} = Projects.create_project(account, user, %{})
    end

    test "create_project/3 enforces unique name within account", %{account: account, user: user} do
      project_fixture(account, user, %{"name" => "Duplicate Name"})

      assert {:error, changeset} =
               Projects.create_project(account, user, %{"name" => "Duplicate Name"})

      errors = errors_on(changeset)
      assert "has already been taken" in (errors[:name] || errors[:account_id] || [])
    end

    test "create_project/3 enforces unique task_prefix within account", %{
      account: account,
      user: user
    } do
      project_fixture(account, user, %{"task_prefix" => "DUP"})

      assert {:error, changeset} =
               Projects.create_project(account, user, %{
                 "name" => "Another Project",
                 "task_prefix" => "DUP"
               })

      errors = errors_on(changeset)
      assert "has already been taken" in (errors[:task_prefix] || errors[:account_id] || [])
    end

    test "update_project/2 with valid data updates the project", %{account: account, user: user} do
      project = project_fixture(account, user)
      update_attrs = %{"name" => "Updated Project", "status" => "active"}

      assert {:ok, %Project{} = project} = Projects.update_project(project, update_attrs)
      assert project.name == "Updated Project"
      assert project.status == "active"
    end

    test "delete_project/1 deletes the project", %{account: account, user: user} do
      project = project_fixture(account, user)
      assert {:ok, %Project{}} = Projects.delete_project(project)
      assert Projects.get_project(project.id) == nil
    end
  end

  describe "tasks" do
    setup do
      account = account_fixture()
      user = user_fixture(%{account_id: account.id})
      project = project_fixture(account, user)

      %{account: account, user: user, project: project}
    end

    test "list_tasks/1 returns all tasks for a project", %{project: project, user: user} do
      task1 = task_fixture(project, user)
      task2 = task_fixture(project, user)

      tasks = Projects.list_tasks(project)
      assert length(tasks) == 2
      assert Enum.any?(tasks, &(&1.id == task1.id))
      assert Enum.any?(tasks, &(&1.id == task2.id))
    end

    test "get_task/2 returns the task with given id", %{project: project, user: user} do
      task = task_fixture(project, user)
      assert Projects.get_task(project, task.id) == task
    end

    test "create_task/3 with valid data creates a task", %{project: project, user: user} do
      valid_attrs = %{
        "title" => "Test Task",
        "description" => "A test task",
        "priority" => "high"
      }

      assert {:ok, %Task{} = task} = Projects.create_task(project, user, valid_attrs)
      assert task.title == "Test Task"
      assert task.description == "A test task"
      assert task.priority == "high"
      assert task.project_id == project.id
      assert task.created_by_id == user.id
      assert task.status == "todo"
      assert task.task_number == 1
      assert String.starts_with?(task.task_identifier, project.task_prefix)
      assert String.ends_with?(task.task_identifier, "-1")
    end

    test "create_task/3 increments task numbers", %{project: project, user: user} do
      {:ok, task1} = Projects.create_task(project, user, %{"title" => "Task 1"})
      {:ok, task2} = Projects.create_task(project, user, %{"title" => "Task 2"})

      assert task1.task_number == 1
      assert String.ends_with?(task1.task_identifier, "-1")
      assert task2.task_number == 2
      assert String.ends_with?(task2.task_identifier, "-2")
    end

    test "create_task/3 uses custom task prefix", %{account: account, user: user} do
      project = project_fixture(account, user, %{"task_prefix" => "FEAT"})
      {:ok, task} = Projects.create_task(project, user, %{"title" => "Feature Task"})

      assert task.task_identifier == "FEAT-1"
    end

    test "update_task_status/2 updates task status and completion time", %{
      project: project,
      user: user
    } do
      task = task_fixture(project, user)

      assert {:ok, %Task{} = updated_task} = Projects.update_task_status(task, "done")
      assert updated_task.status == "done"
      assert updated_task.completed_at != nil

      assert {:ok, %Task{} = reverted_task} = Projects.update_task_status(updated_task, "todo")
      assert reverted_task.status == "todo"
      assert reverted_task.completed_at == nil
    end

    test "list_user_tasks/2 returns tasks assigned to user", %{
      account: account,
      project: project,
      user: user
    } do
      other_user = user_fixture(%{account_id: account.id})

      user_task = task_fixture(project, user, %{"assignee_id" => user.id})
      _other_task = task_fixture(project, user, %{"assignee_id" => other_user.id})

      user_tasks = Projects.list_user_tasks(account, user)
      assert length(user_tasks) == 1
      assert hd(user_tasks).id == user_task.id
    end
  end

  # Test fixtures
  defp project_fixture(account, user, attrs \\ %{}) do
    unique_id = System.unique_integer([:positive]) |> rem(999)

    default_attrs = %{
      "name" => "Test Project #{unique_id}",
      "description" => "A test project",
      "task_prefix" => "T#{unique_id}"
    }

    attrs = Map.merge(default_attrs, attrs)
    {:ok, project} = Projects.create_project(account, user, attrs)
    project
  end

  defp task_fixture(project, user, attrs \\ %{}) do
    default_attrs = %{
      "title" => "Test Task #{System.unique_integer()}",
      "description" => "A test task"
    }

    attrs = Map.merge(default_attrs, attrs)
    {:ok, task} = Projects.create_task(project, user, attrs)
    task
  end
end
