defmodule Codebake.Projects.TaskTest do
  use Codebake.DataCase

  import Codebake.AccountsFixtures
  alias Codebake.Projects.Task

  describe "create_changeset/2" do
    test "valid changeset with required fields" do
      account = account_fixture()
      user = user_fixture(%{account_id: account.id})
      project = project_fixture(account, user)

      attrs = %{
        title: "Test Task",
        project_id: project.id,
        created_by_id: user.id
      }

      changeset = Task.create_changeset(%Task{}, attrs)
      assert changeset.valid?
    end

    test "requires title" do
      changeset = Task.create_changeset(%Task{}, %{})
      assert "can't be blank" in errors_on(changeset).title
    end

    test "requires project_id" do
      changeset = Task.create_changeset(%Task{}, %{title: "Test"})
      assert "can't be blank" in errors_on(changeset).project_id
    end

    test "requires created_by_id" do
      changeset = Task.create_changeset(%Task{}, %{title: "Test"})
      assert "can't be blank" in errors_on(changeset).created_by_id
    end

    test "validates title length" do
      # Too short
      changeset = Task.create_changeset(%Task{}, %{title: "A"})
      assert "should be at least 2 character(s)" in errors_on(changeset).title

      # Too long
      long_title = String.duplicate("a", 201)
      changeset = Task.create_changeset(%Task{}, %{title: long_title})
      assert "should be at most 200 character(s)" in errors_on(changeset).title
    end

    test "validates description length" do
      long_description = String.duplicate("a", 2001)
      changeset = Task.create_changeset(%Task{}, %{description: long_description})
      assert "should be at most 2000 character(s)" in errors_on(changeset).description
    end

    test "validates priority inclusion" do
      changeset = Task.create_changeset(%Task{}, %{priority: "invalid"})
      assert "is invalid" in errors_on(changeset).priority
    end

    test "validates estimated_hours is positive" do
      changeset = Task.create_changeset(%Task{}, %{estimated_hours: -1})
      assert "must be greater than 0" in errors_on(changeset).estimated_hours

      changeset = Task.create_changeset(%Task{}, %{estimated_hours: 0})
      assert "must be greater than 0" in errors_on(changeset).estimated_hours

      changeset = Task.create_changeset(%Task{}, %{estimated_hours: 1})
      refute :estimated_hours in Map.keys(errors_on(changeset))
    end

    test "validates parent_task_id is not self" do
      task_id = UUIDv7.generate()
      changeset = Task.create_changeset(%Task{id: task_id}, %{parent_task_id: task_id})
      assert "cannot be the same as the task itself" in errors_on(changeset).parent_task_id
    end

    test "sets default values" do
      changeset = Task.create_changeset(%Task{}, %{title: "Test"})
      assert Ecto.Changeset.get_field(changeset, :status) == "todo"
      assert Ecto.Changeset.get_field(changeset, :priority) == "medium"
      assert Ecto.Changeset.get_field(changeset, :tags) == []
    end
  end

  describe "update_changeset/2" do
    test "allows updating most fields" do
      task = %Task{title: "Old Title", priority: "low"}

      attrs = %{
        title: "New Title",
        description: "New description",
        priority: "high",
        estimated_hours: 5.5,
        actual_hours: 3.0,
        tags: ["bug", "urgent"]
      }

      changeset = Task.update_changeset(task, attrs)
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :title) == "New Title"
      assert Ecto.Changeset.get_change(changeset, :priority) == "high"
    end

    test "does not allow updating status directly" do
      task = %Task{status: "todo"}
      changeset = Task.update_changeset(task, %{status: "done"})
      refute Ecto.Changeset.get_change(changeset, :status)
    end
  end

  describe "status_changeset/2" do
    test "updates status" do
      task = %Task{status: "todo"}
      changeset = Task.status_changeset(task, "in_progress")
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :status) == "in_progress"
    end

    test "validates status inclusion" do
      task = %Task{}
      changeset = Task.status_changeset(task, "invalid")
      assert "is invalid" in errors_on(changeset).status
    end

    test "sets completed_at when status is done" do
      task = %Task{status: "todo", completed_at: nil}
      changeset = Task.status_changeset(task, "done")
      completed_at = Ecto.Changeset.get_change(changeset, :completed_at)
      assert completed_at != nil
      assert DateTime.diff(DateTime.utc_now(), completed_at, :second) < 2
    end

    test "clears completed_at when status is not done" do
      completed_time = DateTime.utc_now()
      task = %Task{status: "done", completed_at: completed_time}
      changeset = Task.status_changeset(task, "todo")
      assert Ecto.Changeset.get_change(changeset, :completed_at) == nil
    end
  end

  describe "set_task_number_changeset/3" do
    test "sets task number and identifier" do
      task = %Task{}
      changeset = Task.set_task_number_changeset(task, 42, "PROJ-42")
      assert Ecto.Changeset.get_change(changeset, :task_number) == 42
      assert Ecto.Changeset.get_change(changeset, :task_identifier) == "PROJ-42"
    end
  end

  describe "helper functions" do
    test "completed?/1" do
      assert Task.completed?(%Task{status: "done"})
      refute Task.completed?(%Task{status: "todo"})
      refute Task.completed?(%Task{status: "in_progress"})
      refute Task.completed?(%Task{status: "blocked"})
    end

    test "blocked?/1" do
      assert Task.blocked?(%Task{status: "blocked"})
      refute Task.blocked?(%Task{status: "todo"})
      refute Task.blocked?(%Task{status: "in_progress"})
      refute Task.blocked?(%Task{status: "done"})
    end

    test "in_progress?/1" do
      assert Task.in_progress?(%Task{status: "in_progress"})
      refute Task.in_progress?(%Task{status: "todo"})
      refute Task.in_progress?(%Task{status: "blocked"})
      refute Task.in_progress?(%Task{status: "done"})
    end

    test "overdue?/1" do
      past_date = DateTime.utc_now() |> DateTime.add(-1, :day)
      future_date = DateTime.utc_now() |> DateTime.add(1, :day)

      # No due date
      refute Task.overdue?(%Task{due_date: nil, status: "todo"})

      # Past due date, not completed
      assert Task.overdue?(%Task{due_date: past_date, status: "todo"})
      assert Task.overdue?(%Task{due_date: past_date, status: "in_progress"})

      # Past due date, but completed
      refute Task.overdue?(%Task{due_date: past_date, status: "done"})

      # Future due date
      refute Task.overdue?(%Task{due_date: future_date, status: "todo"})
    end

    test "subtask?/1" do
      parent_id = UUIDv7.generate()
      assert Task.subtask?(%Task{parent_task_id: parent_id})
      refute Task.subtask?(%Task{parent_task_id: nil})
    end
  end

  # Helper function to create a project for testing
  defp project_fixture(account, user) do
    unique_id = System.unique_integer([:positive]) |> rem(999)

    {:ok, project} =
      Codebake.Projects.create_project(account, user, %{
        "name" => "Test Project #{unique_id}",
        "task_prefix" => "T#{unique_id}"
      })

    project
  end
end
