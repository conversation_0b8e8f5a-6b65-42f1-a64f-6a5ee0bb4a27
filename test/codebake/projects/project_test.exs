defmodule Codebake.Projects.ProjectTest do
  use Codebake.DataCase

  import Codebake.AccountsFixtures
  alias Codebake.Projects.Project

  describe "create_changeset/2" do
    test "valid changeset with required fields" do
      account = account_fixture()
      user = user_fixture(%{account_id: account.id})

      attrs = %{
        name: "Test Project",
        account_id: account.id,
        created_by_id: user.id
      }

      changeset = Project.create_changeset(%Project{}, attrs)
      assert changeset.valid?
    end

    test "requires name" do
      changeset = Project.create_changeset(%Project{}, %{})
      assert "can't be blank" in errors_on(changeset).name
    end

    test "requires account_id" do
      changeset = Project.create_changeset(%Project{}, %{name: "Test"})
      assert "can't be blank" in errors_on(changeset).account_id
    end

    test "requires created_by_id" do
      changeset = Project.create_changeset(%Project{}, %{name: "Test"})
      assert "can't be blank" in errors_on(changeset).created_by_id
    end

    test "validates name length" do
      # Too short
      changeset = Project.create_changeset(%Project{}, %{name: "A"})
      assert "should be at least 2 character(s)" in errors_on(changeset).name

      # Too long
      long_name = String.duplicate("a", 101)
      changeset = Project.create_changeset(%Project{}, %{name: long_name})
      assert "should be at most 100 character(s)" in errors_on(changeset).name
    end

    test "validates description length" do
      long_description = String.duplicate("a", 1001)
      changeset = Project.create_changeset(%Project{}, %{description: long_description})
      assert "should be at most 1000 character(s)" in errors_on(changeset).description
    end

    test "validates task_prefix format" do
      # Invalid: starts with number
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "1TEST"})

      assert "must start with a letter and contain only uppercase letters and numbers" in errors_on(
               changeset
             ).task_prefix

      # Invalid: contains lowercase
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "test"})

      assert "must start with a letter and contain only uppercase letters and numbers" in errors_on(
               changeset
             ).task_prefix

      # Invalid: contains special characters
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "TEST-"})

      assert "must start with a letter and contain only uppercase letters and numbers" in errors_on(
               changeset
             ).task_prefix

      # Valid
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "TEST123"})
      refute :task_prefix in Map.keys(errors_on(changeset))
    end

    test "validates task_prefix length" do
      # Too short
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "A"})
      assert "should be at least 2 character(s)" in errors_on(changeset).task_prefix

      # Too long
      changeset = Project.create_changeset(%Project{}, %{task_prefix: "VERYLONGPREFIX"})
      assert "should be at most 10 character(s)" in errors_on(changeset).task_prefix
    end

    test "sets default task_prefix" do
      changeset = Project.create_changeset(%Project{}, %{name: "Test"})
      assert Ecto.Changeset.get_field(changeset, :task_prefix) == "TASK"
    end
  end

  describe "update_changeset/2" do
    test "allows updating name, description, status, and settings" do
      project = %Project{name: "Old Name", status: "planning"}

      attrs = %{
        name: "New Name",
        description: "New description",
        status: "active",
        settings: %{key: "value"}
      }

      changeset = Project.update_changeset(project, attrs)
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :name) == "New Name"
      assert Ecto.Changeset.get_change(changeset, :status) == "active"
    end

    test "validates status inclusion" do
      project = %Project{}
      changeset = Project.update_changeset(project, %{status: "invalid"})
      assert "is invalid" in errors_on(changeset).status
    end

    test "does not allow updating task_prefix" do
      project = %Project{task_prefix: "OLD"}
      changeset = Project.update_changeset(project, %{task_prefix: "NEW"})
      refute Ecto.Changeset.get_change(changeset, :task_prefix)
    end
  end

  describe "task_prefix_changeset/2" do
    test "allows updating task_prefix" do
      project = %Project{task_prefix: "OLD"}
      changeset = Project.task_prefix_changeset(project, %{task_prefix: "NEW"})
      assert changeset.valid?
      assert Ecto.Changeset.get_change(changeset, :task_prefix) == "NEW"
    end
  end

  describe "increment_task_counter_changeset/1" do
    test "increments task counter from 0" do
      project = %Project{task_counter: 0}
      changeset = Project.increment_task_counter_changeset(project)
      assert Ecto.Changeset.get_change(changeset, :task_counter) == 1
    end

    test "increments task counter from existing value" do
      project = %Project{task_counter: 5}
      changeset = Project.increment_task_counter_changeset(project)
      assert Ecto.Changeset.get_change(changeset, :task_counter) == 6
    end

    test "handles nil task counter" do
      project = %Project{task_counter: nil}
      changeset = Project.increment_task_counter_changeset(project)
      assert Ecto.Changeset.get_change(changeset, :task_counter) == 1
    end
  end

  describe "helper functions" do
    test "next_task_number/1" do
      assert Project.next_task_number(%Project{task_counter: 0}) == 1
      assert Project.next_task_number(%Project{task_counter: 5}) == 6
      assert Project.next_task_number(%Project{task_counter: nil}) == 1
    end

    test "generate_task_identifier/2" do
      project = %Project{task_prefix: "PROJ"}
      assert Project.generate_task_identifier(project, 1) == "PROJ-1"
      assert Project.generate_task_identifier(project, 42) == "PROJ-42"
    end

    test "active?/1" do
      assert Project.active?(%Project{status: "planning"})
      assert Project.active?(%Project{status: "active"})
      assert Project.active?(%Project{status: "on_hold"})
      refute Project.active?(%Project{status: "completed"})
      refute Project.active?(%Project{status: "archived"})
    end

    test "accepts_tasks?/1" do
      assert Project.accepts_tasks?(%Project{status: "planning"})
      assert Project.accepts_tasks?(%Project{status: "active"})
      refute Project.accepts_tasks?(%Project{status: "on_hold"})
      refute Project.accepts_tasks?(%Project{status: "completed"})
      refute Project.accepts_tasks?(%Project{status: "archived"})
    end
  end
end
