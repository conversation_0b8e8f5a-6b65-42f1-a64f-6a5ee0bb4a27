defmodule Codebake.Projects.PhasesTest do
  use Codebake.DataCase, async: true

  import Codebake.Factory

  alias Codebake.Projects

  describe "settings-backed phases" do
    test "list_phases/1 returns [] by default" do
      project = insert(:project)
      assert Projects.list_phases(project) == []
      assert Projects.current_phase(project) == nil
    end

    test "add_phase/2 appends a phase and sets current for the first phase" do
      project = insert(:project)

      assert {:ok, project} = Projects.add_phase(project, "Discovery")
      phases = Projects.list_phases(project)
      assert length(phases) == 1
      assert hd(phases)["name"] == "Discovery"

      # current is set to the first phase
      assert Projects.current_phase(project)["name"] == "Discovery"

      # add another phase
      assert {:ok, project} = Projects.add_phase(project, "Build")
      phases = Projects.list_phases(project)
      assert length(phases) == 2

      # current remains Discovery
      assert Projects.current_phase(project)["name"] == "Discovery"
    end

    test "set_current_phase/2 switches current phase when valid" do
      project = insert(:project)

      {:ok, project} = Projects.add_phase(project, "Alpha")
      {:ok, project} = Projects.add_phase(project, "Beta")

      phases = Projects.list_phases(project)
      beta = Enum.find(phases, &(&1["name"] == "Beta"))

      assert {:ok, project} = Projects.set_current_phase(project, beta["id"])
      assert Projects.current_phase(project)["name"] == "Beta"
    end

    test "add_phase/2 rejects blank names" do
      project = insert(:project)
      assert {:error, %Ecto.Changeset{}} = Projects.add_phase(project, " ")
      assert Projects.list_phases(project) == []
    end

    test "set_current_phase/2 returns error for unknown id" do
      project = insert(:project)
      assert {:error, :not_found} = Projects.set_current_phase(project, Ecto.UUID.generate())
    end
  end
end
