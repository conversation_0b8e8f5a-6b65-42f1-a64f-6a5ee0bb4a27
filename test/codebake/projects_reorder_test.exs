defmodule Codebake.ProjectsReorderTest do
  use Codebake.DataCase, async: true

  import Ecto.Query
  alias <PERSON><PERSON><PERSON>.Projects
  alias <PERSON>bake.Projects.{Project, Task}
  alias <PERSON>bake.Factory

  describe "reorder_and_move_task/4" do
    test "reorders within same status before a target" do
      %{project: project, owner: owner} = Factory.project_with_owner()

      {:ok, a} = Projects.create_task(project, owner, %{"title" => "AA", "status" => "todo"})
      {:ok, b} = Projects.create_task(project, owner, %{"title" => "BB", "status" => "todo"})
      {:ok, c} = Projects.create_task(project, owner, %{"title" => "CC", "status" => "todo"})

      assert {a.position, b.position, c.position} == {1, 2, 3}

      assert {:ok, %Task{} = _} = Projects.reorder_and_move_task(project, c, "todo", b.id)

      # Fetch tasks ordered by position
      todos =
        from(t in Task,
          where: t.project_id == ^project.id and t.status == "todo",
          order_by: [asc: t.position]
        )
        |> Codebake.Repo.all()

      assert Enum.map(todos, & &1.title) == ["AA", "CC", "BB"]
      assert Enum.map(todos, & &1.position) == [1, 2, 3]
    end

    test "moves across statuses and appends when before_id is nil" do
      %{project: project, owner: owner} = Factory.project_with_owner()

      {:ok, a} = Projects.create_task(project, owner, %{"title" => "AA", "status" => "todo"})
      {:ok, b} = Projects.create_task(project, owner, %{"title" => "BB", "status" => "todo"})

      {:ok, x} =
        Projects.create_task(project, owner, %{"title" => "XX", "status" => "in_progress"})

      assert {a.position, b.position} == {1, 2}
      assert x.position == 1

      assert {:ok, %Task{} = moved} =
               Projects.reorder_and_move_task(project, b, "in_progress", nil)

      assert moved.status == "in_progress"

      in_progress =
        from(t in Task,
          where: t.project_id == ^project.id and t.status == "in_progress",
          order_by: [asc: t.position]
        )
        |> Codebake.Repo.all()

      assert Enum.map(in_progress, & &1.title) == ["XX", "BB"]
      assert Enum.map(in_progress, & &1.position) == [1, 2]

      todos =
        from(t in Task,
          where: t.project_id == ^project.id and t.status == "todo",
          order_by: [asc: t.position]
        )
        |> Codebake.Repo.all()

      assert Enum.map(todos, & &1.title) == ["AA"]
      assert Enum.map(todos, & &1.position) == [1]
    end
  end

  test "moving unassigned task to in_progress assigns to provided user id" do
    %{project: project, owner: owner} = Factory.project_with_owner()

    {:ok, t} = Projects.create_task(project, owner, %{"title" => "UA", "status" => "todo"})
    assert is_nil(t.assignee_id)

    assert {:ok, moved} =
             Projects.reorder_and_move_task(project, t, "in_progress", nil,
               assign_if_unassigned_to: owner.id
             )

    assert moved.status == "in_progress"
    assert moved.assignee_id == owner.id
  end

  test "moving already assigned task to in_progress does not overwrite assignee" do
    %{project: project, owner: owner} = Factory.project_with_owner()
    other = Factory.insert(:user, account: project.account)

    {:ok, t} =
      Projects.create_task(project, owner, %{
        "title" => "AA",
        "status" => "todo",
        "assignee_id" => other.id
      })

    assert {:ok, moved} =
             Projects.reorder_and_move_task(project, t, "in_progress", nil,
               assign_if_unassigned_to: owner.id
             )

    assert moved.status == "in_progress"
    assert moved.assignee_id == other.id
  end

  test "moving assigned task to todo unassigns the task" do
    %{project: project, owner: owner} = Factory.project_with_owner()
    other = Factory.insert(:user, account: project.account)

    {:ok, t} =
      Projects.create_task(project, owner, %{
        "title" => "Assigned",
        "status" => "in_progress",
        "assignee_id" => other.id
      })

    assert {:ok, moved} =
             Projects.reorder_and_move_task(project, t, "todo", nil,
               unassign_if_moved_to_todo: true
             )

    assert moved.status == "todo"
    assert moved.assignee_id == nil
  end

  test "moving unassigned task to todo keeps it unassigned" do
    %{project: project, owner: owner} = Factory.project_with_owner()

    {:ok, t} = Projects.create_task(project, owner, %{"title" => "UA", "status" => "blocked"})

    assert is_nil(t.assignee_id)

    assert {:ok, moved} =
             Projects.reorder_and_move_task(project, t, "todo", nil,
               unassign_if_moved_to_todo: true
             )

    assert moved.status == "todo"
    assert moved.assignee_id == nil
  end
end
