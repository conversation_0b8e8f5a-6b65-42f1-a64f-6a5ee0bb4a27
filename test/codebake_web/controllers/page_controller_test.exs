defmodule CodebakeWeb.PageControllerTest do
  use CodebakeWeb.ConnCase

  import Codebake.AccountsFixtures

  describe "GET /" do
    test "redirects unauthenticated users to login page", %{conn: conn} do
      conn = get(conn, ~p"/")
      assert redirected_to(conn) == ~p"/users/log-in"
    end

    test "redirects authenticated users with no account setup to onboarding", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      conn = get(conn, ~p"/")
      assert redirected_to(conn) == ~p"/onboarding/account"
    end

    test "redirects authenticated users with projects to account dashboard", %{conn: conn} do
      account = account_fixture()
      user = user_fixture(%{account_id: account.id})

      # Create a project for the user
      {:ok, _project} =
        Codebake.Projects.create_project(account, user, %{
          "name" => "Test Project",
          "task_prefix" => "TEST"
        })

      conn = log_in_user(conn, user)

      conn = get(conn, ~p"/")
      assert redirected_to(conn) == ~p"/#{account.slug}"
    end
  end
end
