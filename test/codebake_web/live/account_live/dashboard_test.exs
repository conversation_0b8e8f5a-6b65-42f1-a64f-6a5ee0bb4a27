defmodule CodebakeWeb.AccountLive.DashboardTest do
  use CodebakeWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Codebake.AccountsFixtures

  alias Codebake.Projects

  describe "Account Dashboard" do
    setup do
      account = account_fixture(%{name: "Misfits", slug: "misfits"})
      user = user_fixture(%{account_id: account.id})

      # Create projects directly in the account
      {:ok, project1} =
        Projects.create_project(account, user, %{
          "name" => "Engineering Project",
          "description" => "A project for engineering work",
          "task_prefix" => "ENG"
        })

      {:ok, project2} =
        Projects.create_project(account, user, %{
          "name" => "Marketing Project",
          "description" => "A project for marketing work",
          "task_prefix" => "MKT"
        })

      %{
        account: account,
        user: user,
        project1: project1,
        project2: project2
      }
    end

    test "renders account dashboard with project overview", %{
      conn: conn,
      user: user,
      account: account
    } do
      conn = log_in_user(conn, user)

      {:ok, _view, html} = live(conn, ~p"/#{account.slug}")

      # Check account information
      assert html =~ account.name
      # Check projects are listed
      assert html =~ "Engineering Project"
      assert html =~ "Marketing Project"
    end

    test "shows project information", %{conn: conn, user: user, account: account} do
      conn = log_in_user(conn, user)

      {:ok, _view, html} = live(conn, ~p"/#{account.slug}")

      # Should show project counts (even if 0)
      assert html =~ "projects"
    end

    test "redirects unauthorized users", %{conn: conn, account: account} do
      # Create a user with a different account
      other_account = account_fixture()
      other_user = user_fixture(%{account_id: other_account.id})
      conn = log_in_user(conn, other_user)

      # Should redirect unauthorized users to home page
      result = live(conn, ~p"/#{account.slug}")
      assert {:error, {:redirect, %{to: redirect_path}}} = result
      assert redirect_path == "/"
    end

    test "project links use correct URL structure", %{conn: conn, user: user, account: account} do
      conn = log_in_user(conn, user)

      {:ok, _view, html} = live(conn, ~p"/#{account.slug}")

      # Project links should use account/projects format
      assert html =~ "/#{account.slug}/projects"
    end
  end

  describe "Create project from dashboard" do
    setup do
      account = account_fixture(%{name: "Acme Corp", slug: "acme-corp"})
      user = user_fixture(%{account_id: account.id})
      %{account: account, user: user}
    end

    test "user can create a new project via the dashboard flow", %{
      conn: conn,
      user: user,
      account: account
    } do
      conn = log_in_user(conn, user)

      # 1) Land on the Account Dashboard and ensure the Create Project CTA is present
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}")
      assert has_element?(view, "a[href='#{~p"/#{account.slug}/projects/new"}']")

      # 2) Follow the link target and submit the New Project form
      {:ok, new_view, _html} = live(conn, ~p"/#{account.slug}/projects/new")

      new_view
      |> form("form[phx-submit='create_project']",
        project: %{name: "Dashboard Project", task_prefix: "DASH"}
      )
      |> render_submit()

      # 3) Behavior check: the project is actually created and appears on the dashboard
      projects = Codebake.Projects.list_projects(account)
      assert Enum.any?(projects, &(&1.name == "Dashboard Project"))

      {:ok, _dash_view, dash_html} = live(conn, ~p"/#{account.slug}")
      assert dash_html =~ "Dashboard Project"
    end
  end
end
