defmodule CodebakeWeb.OnboardingLive.AccountTest do
  use CodebakeWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Codebake.AccountsFixtures

  alias Codebake.Projects

  describe "Account Onboarding" do
    test "redirects to onboarding when user has no account", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      # User should be redirected to onboarding when accessing main app
      conn = get(conn, ~p"/")
      assert redirected_to(conn) == ~p"/onboarding/account"
    end

    test "redirects to onboarding when user has auto-generated account", %{conn: conn} do
      user = user_fixture()
      # Create an auto-generated looking account
      account = account_fixture(%{name: "#{user.email}'s Account", slug: "user-123"})
      user = %{user | account_id: account.id}

      conn = log_in_user(conn, user)

      # User should be redirected to onboarding
      conn = get(conn, ~p"/")
      assert redirected_to(conn) == ~p"/onboarding/account"
    end

    test "allows access when user has proper account", %{conn: conn} do
      account = account_fixture(%{name: "Misfits", slug: "misfits"})
      user = user_fixture(%{account_id: account.id})

      # Create a project and add user as member so they can access the account
      {:ok, project} =
        Projects.create_project(account, user, %{
          "name" => "Test Project",
          "task_prefix" => "TEST"
        })

      conn = log_in_user(conn, user)

      # User should be able to access their account dashboard
      {:ok, _view, _html} = live(conn, ~p"/#{account.slug}")
    end

    test "renders onboarding form for new user", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      {:ok, _view, html} = live(conn, ~p"/onboarding/account")

      assert html =~ "Welcome to Codebake!"
      assert html =~ "Account Name"
      assert html =~ "Create Account"
    end

    test "creates account and redirects on successful submission", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      {:ok, view, _html} = live(conn, ~p"/onboarding/account")

      # Submit the form with just the required name field
      result =
        view
        |> form("form",
          account: %{
            name: "Misfits"
          }
        )
        |> render_submit()

      # Should redirect to account dashboard
      assert {:error, {:redirect, %{to: redirect_path}}} = result
      assert String.starts_with?(redirect_path, "/misfits")
    end

    test "updates existing account when user has auto-generated account", %{conn: conn} do
      user = user_fixture()
      # Create an auto-generated account
      account = account_fixture(%{name: "#{user.email}'s Account", slug: "user-123"})
      {:ok, user} = Codebake.Accounts.update_user(user, %{account_id: account.id})

      conn = log_in_user(conn, user)

      {:ok, view, html} = live(conn, ~p"/onboarding/account")

      # Should show "Update Account" instead of "Create Account"
      assert html =~ "Update Account"

      # Submit the form with just the required name field
      result =
        view
        |> form("form",
          account: %{
            name: "Misfits"
          }
        )
        |> render_submit()

      # Should redirect to updated account dashboard
      assert {:error, {:redirect, %{to: redirect_path}}} = result
      assert String.starts_with?(redirect_path, "/misfits")
    end

    test "allows skipping onboarding", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      {:ok, view, _html} = live(conn, ~p"/onboarding/account")

      # Click skip button
      result =
        view
        |> element("button", "Skip for now")
        |> render_click()

      # Should redirect to account dashboard with generated account
      assert {:error, {:redirect, %{to: redirect_path}}} = result
      assert String.match?(redirect_path, ~r/^\/[a-z]+-[a-z]+-\d+$/)
    end

    test "shows URL preview when name is entered", %{conn: conn} do
      user = user_fixture()
      conn = log_in_user(conn, user)

      {:ok, view, _html} = live(conn, ~p"/onboarding/account")

      # Type in account name
      html =
        view
        |> form("form", account: %{name: "Misfits"})
        |> render_change()

      # Should show URL preview
      assert html =~ "/misfits"
      assert html =~ "Your account URL will be"
    end
  end

  describe "Account Setup Detection" do
    test "user_needs_account_setup? returns true for user with no account" do
      user = user_fixture()
      assert Codebake.Accounts.user_needs_account_setup?(user)
    end

    test "user_needs_account_setup? returns true for auto-generated account names" do
      user = user_fixture()
      account = account_fixture(%{name: "#{user.email}'s Account"})
      {:ok, user} = Codebake.Accounts.update_user(user, %{account_id: account.id})

      assert Codebake.Accounts.user_needs_account_setup?(user)
    end

    test "user_needs_account_setup? returns false for proper account names" do
      user = user_fixture()
      account = account_fixture(%{name: "Misfits"})
      {:ok, user} = Codebake.Accounts.update_user(user, %{account_id: account.id})

      refute Codebake.Accounts.user_needs_account_setup?(user)
    end
  end
end
