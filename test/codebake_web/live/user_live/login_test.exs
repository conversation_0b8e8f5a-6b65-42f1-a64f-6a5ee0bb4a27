defmodule CodebakeWeb.UserLive.LoginTest do
  use CodebakeWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Codebake.AccountsFixtures

  describe "login page" do
    test "renders login page", %{conn: conn} do
      {:ok, _lv, html} = live(conn, ~p"/users/log-in")

      assert html =~ "Log in"
      assert html =~ "Sign up"
      assert html =~ "Log in with email"
    end
  end

  describe "user login - magic link" do
    test "sends magic link email when user exists", %{conn: conn} do
      user = user_fixture()

      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      {:ok, _lv, html} =
        form(lv, "#login_form_magic", user: %{email: user.email})
        |> render_submit()
        |> follow_redirect(conn, ~p"/users/log-in")

      # Positive flash hidden; assert we are back on the login page
      assert html =~ "Log in"

      assert Codebake.Repo.get_by!(Codebake.Accounts.UserToken, user_id: user.id).context ==
               "login"
    end

    test "asks to create account when email not found", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      html =
        form(lv, "#login_form_magic", user: %{email: "<EMAIL>"})
        |> render_submit()

      assert html =~ "create-account-prompt"
      assert html =~ "No account <NAME_EMAIL>"
    end

    test "creates account and sends magic link when user confirms create", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      _html =
        form(lv, "#login_form_magic", user: %{email: "<EMAIL>"})
        |> render_submit()

      {:ok, _lv, html} =
        lv
        |> element("#create-account-prompt button", "Create account")
        |> render_click()
        |> follow_redirect(conn, ~p"/users/log-in")

      # Positive flash hidden; assert we are back on the login page
      assert html =~ "Log in"

      user = Codebake.Repo.get_by!(Codebake.Accounts.User, email: "<EMAIL>")

      assert Codebake.Repo.get_by!(Codebake.Accounts.UserToken, user_id: user.id).context ==
               "login"
    end
  end

  describe "user login - password" do
    # password login UI hidden for now
    @tag :skip
    test "redirects if user logs in with valid credentials", %{conn: conn} do
      user = user_fixture() |> set_password()

      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      form =
        form(lv, "#login_form_password", %{
          "user" => %{
            "email" => user.email,
            "password" => valid_user_password(),
            "remember_me" => "true"
          }
        })

      conn = submit_form(form, conn)

      assert redirected_to(conn) == ~p"/"
    end

    # password login UI hidden for now
    @tag :skip
    test "redirects to login page with a flash error if credentials are invalid", %{
      conn: conn
    } do
      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      form =
        form(lv, "#login_form_password", %{
          "user" => %{"email" => "<EMAIL>", "password" => "123456"}
        })

      render_submit(form, %{"user" => %{"remember_me" => "true"}})

      conn = follow_trigger_action(form, conn)
      assert Phoenix.Flash.get(conn.assigns.flash, :error) == "Invalid email or password"
      assert redirected_to(conn) == ~p"/users/log-in"
    end
  end

  describe "login navigation" do
    test "redirects to registration page when the Register button is clicked", %{conn: conn} do
      {:ok, lv, _html} = live(conn, ~p"/users/log-in")

      {:ok, _login_live, login_html} =
        lv
        |> element("main a", "Sign up")
        |> render_click()
        |> follow_redirect(conn, ~p"/users/register")

      assert login_html =~ "Register for an account"
    end
  end

  describe "re-authentication (sudo mode)" do
    setup %{conn: conn} do
      user = user_fixture()
      %{user: user, conn: log_in_user(conn, user)}
    end

    test "shows login page with email filled in", %{conn: conn, user: user} do
      {:ok, _lv, html} = live(conn, ~p"/users/log-in")

      assert html =~ "You need to reauthenticate"
      refute html =~ "Sign up"
      assert html =~ "Log in with email"

      assert html =~
               ~s(<input type="email" name="user[email]" id="login_form_magic_email" value="#{user.email}")
    end
  end
end
