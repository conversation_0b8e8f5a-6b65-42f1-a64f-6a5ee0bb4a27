defmodule CodebakeWeb.ProjectLive.BoardAddTaskTest do
  @moduledoc """
  Behavioral tests for adding a new task to a project's Todo list on the board.
  """

  use CodebakeWeb.ConnCase, async: true
  import Phoenix.LiveViewTest
  import Codebake.AccountsFixtures

  alias Codebake.Projects

  describe "adding a task to the Todo column" do
    setup %{conn: conn} do
      account = account_fixture(%{name: "Mad Labs", slug: "mad-labs"})
      user = user_fixture(%{account_id: account.id})

      {:ok, project} =
        Projects.create_project(account, user, %{
          "name" => "Reanimation Platform",
          "task_prefix" => "SPARK"
        })

      {:ok, conn: log_in_user(conn, user), account: account, user: user, project: project}
    end

    test "user can create a new Todo task via the board modal", %{
      conn: conn,
      account: account,
      project: project
    } do
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}/projects/#{project.id}/board")

      # Open the creation modal in the Todo column
      view
      |> element("#column-todo button.btn-circle")
      |> render_click()

      assert has_element?(view, "form[phx-submit='create_task']")

      title = "Harness celestial lightning #{System.unique_integer([:positive])}"

      view
      |> form("form[phx-submit='create_task']", task: %{title: title, description: "It lives!"})
      |> render_submit()

      # Modal closes and the new task appears on the board (Todo column by default)
      refute has_element?(view, "form[phx-submit='create_task']")
      assert render(view) =~ title
    end

    test "validation errors keep the modal open when title is missing", %{
      conn: conn,
      account: account,
      project: project
    } do
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}/projects/#{project.id}/board")

      view
      |> element("#column-todo button.btn-circle")
      |> render_click()

      assert has_element?(view, "form[phx-submit='create_task']")

      view
      |> form("form[phx-submit='create_task']", task: %{title: "", description: ""})
      |> render_submit()

      # Form remains open and shows an error from changeset validations
      assert has_element?(view, "form[phx-submit='create_task']")
      assert render(view) =~ "Failed to create task"
    end
  end
end
