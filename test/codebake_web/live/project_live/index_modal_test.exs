defmodule CodebakeWeb.ProjectLive.IndexModalTest do
  @moduledoc """
  Test for the new project modal behavior to ensure it doesn't close when typing.
  """

  use CodebakeWeb.ConnCase, async: true
  import Phoenix.LiveViewTest

  alias Codebake.{AccountsFixtures, Projects}

  describe "new project modal" do
    setup do
      account = AccountsFixtures.account_fixture()
      user = AccountsFixtures.user_fixture(%{account_id: account.id})

      # Create a project and add user as member so they can access the account
      {:ok, project} =
        Projects.create_project(account, user, %{
          "name" => "Test Project",
          "task_prefix" => "TEST"
        })

      %{account: account, user: user, project: project}
    end

    test "modal stays open when typing in form fields", %{
      conn: conn,
      user: user,
      account: account
    } do
      # Log in the user
      conn = log_in_user(conn, user)

      # Navigate to the projects page and open the modal
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}/projects/new")

      # Verify the modal is shown
      assert has_element?(view, "[data-testid='create-project-modal']") == false

      # The modal should be shown because we're on the /new route
      assert view |> element("form[phx-submit='create_project']") |> has_element?()

      # Type in the project name field
      view
      |> form("form[phx-submit='create_project']", project: %{name: "Test Project"})
      |> render_change()

      # Verify the modal is still open after typing
      assert view |> element("form[phx-submit='create_project']") |> has_element?()

      # Type in the description field
      view
      |> form("form[phx-submit='create_project']", project: %{description: "Test Description"})
      |> render_change()

      # Verify the modal is still open after typing in description
      assert view |> element("form[phx-submit='create_project']") |> has_element?()

      # Type in the task prefix field
      view
      |> form("form[phx-submit='create_project']", project: %{task_prefix: "TEST"})
      |> render_change()

      # Verify the modal is still open after typing in task prefix
      assert view |> element("form[phx-submit='create_project']") |> has_element?()
    end

    test "modal closes when clicking cancel", %{
      conn: conn,
      user: user,
      account: account
    } do
      # Log in the user
      conn = log_in_user(conn, user)

      # Navigate to the projects page and open the modal
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}/projects/new")

      # Verify the modal is shown
      assert view |> element("form[phx-submit='create_project']") |> has_element?()

      # Click the cancel link
      view |> element("a", "Cancel") |> render_click()

      # Verify the modal is closed (form should no longer be present)
      refute view |> element("form[phx-submit='create_project']") |> has_element?()
    end
  end
end
