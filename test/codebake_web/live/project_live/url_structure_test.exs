defmodule CodebakeWeb.ProjectLive.UrlStructureTest do
  use CodebakeWeb.ConnCase, async: true

  import Phoenix.LiveViewTest
  import Codebake.AccountsFixtures

  alias Codebake.Projects

  describe "URL structure with account and team slugs" do
    setup do
      account = account_fixture(%{name: "Acme Corp", slug: "acme-corp"})
      user = user_fixture(%{account_id: account.id})

      # Create a project and add user as member so they can access the account
      {:ok, project} =
        Projects.create_project(account, user, %{
          "name" => "Test Project",
          "task_prefix" => "TEST"
        })

      %{account: account, user: user, project: project}
    end

    test "projects index uses account slug", %{
      conn: conn,
      user: user,
      account: account
    } do
      conn = log_in_user(conn, user)

      # Test the new URL structure: /account-slug/projects
      {:ok, _view, html} = live(conn, ~p"/#{account.slug}/projects")

      assert html =~ "Projects"
      assert html =~ account.name
    end

    test "projects new modal uses account slug", %{
      conn: conn,
      user: user,
      account: account
    } do
      conn = log_in_user(conn, user)

      # Test the new URL structure: /account-slug/projects/new
      {:ok, view, _html} = live(conn, ~p"/#{account.slug}/projects/new")

      # Verify the modal form is present
      assert view |> element("form[phx-submit='create_project']") |> has_element?()
    end

    test "URLs are human readable", %{account: account} do
      # Verify the URLs are clean and readable
      projects_url = ~p"/#{account.slug}/projects"
      new_project_url = ~p"/#{account.slug}/projects/new"

      assert projects_url == "/#{account.slug}/projects"
      assert new_project_url == "/#{account.slug}/projects/new"
    end
  end
end
