defmodule Codebake.Factory do
  @moduledoc """
  ExMachina factory for creating test data.

  This factory provides convenient functions for creating test records
  with sensible defaults and proper associations.
  """

  use ExMachina.Ecto, repo: Codebake.Repo

  alias Codebake.Accounts.{User, Account}
  alias Codebake.Projects.{Project, ProjectMembership, Task}

  # User factories
  def user_factory do
    %User{
      email: sequence(:email, &"user#{&1}@example.com"),
      name: sequence(:name, &"User #{&1}"),
      hashed_password: Bcrypt.hash_pwd_salt("password123"),
      confirmed_at: DateTime.utc_now() |> DateTime.truncate(:second),
      timezone: "UTC",
      settings: %{},
      avatar_url: nil
    }
  end

  def confirmed_user_factory do
    struct!(
      user_factory(),
      %{
        confirmed_at: DateTime.utc_now() |> DateTime.truncate(:second)
      }
    )
  end

  def unconfirmed_user_factory do
    struct!(
      user_factory(),
      %{
        confirmed_at: nil
      }
    )
  end

  # Account factories
  def account_factory do
    %Account{
      name: sequence(:account_name, &"Test Account #{&1}"),
      slug: sequence(:account_slug, &"test-account-#{&1}"),
      billing_email: sequence(:billing_email, &"billing#{&1}@example.com"),
      subscription_status: "trial",
      subscription_tier: "freemium",
      trial_ends_at: DateTime.utc_now() |> DateTime.add(14, :day) |> DateTime.truncate(:second),
      settings: %{}
    }
  end

  def active_account_factory do
    struct!(
      account_factory(),
      %{
        subscription_status: "active",
        subscription_tier: "team"
      }
    )
  end

  # Project membership factories
  def project_membership_factory do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    %ProjectMembership{
      role: "member",
      invited_at: now,
      joined_at: now,
      project: build(:project),
      user: build(:user),
      invited_by: build(:user)
    }
  end

  def owner_project_membership_factory do
    struct!(
      project_membership_factory(),
      %{
        role: "owner"
      }
    )
  end

  def admin_project_membership_factory do
    struct!(
      project_membership_factory(),
      %{
        role: "admin"
      }
    )
  end

  def viewer_project_membership_factory do
    struct!(
      project_membership_factory(),
      %{
        role: "viewer"
      }
    )
  end

  # Project factories
  def project_factory do
    %Project{
      name: sequence(:project_name, &"Test Project #{&1}"),
      description: "A test project",
      task_prefix: sequence(:task_prefix, &"TP#{&1}"),
      status: "planning",
      task_counter: 0,
      settings: %{},
      account: build(:account),
      created_by: build(:user)
    }
  end

  def active_project_factory do
    struct!(
      project_factory(),
      %{
        status: "active"
      }
    )
  end

  # Task factories
  def task_factory do
    phase_id = "********-0000-0000-0000-************"

    %Task{
      title: sequence(:task_title, &"Test Task #{&1}"),
      description: "A test task",
      status: "todo",
      priority: "medium",
      task_number: sequence(:task_number, & &1),
      task_identifier: sequence(:task_identifier, &"TASK-#{&1}"),
      tags: [],
      metadata: %{},
      project:
        build(
          :project,
          settings: %{
            "phases" => [%{"id" => phase_id, "name" => "Initial", "position" => 0}],
            "current_phase_id" => phase_id
          }
        ),
      phase_id: phase_id,
      created_by: build(:user)
    }
  end

  def in_progress_task_factory do
    struct!(
      task_factory(),
      %{
        status: "in_progress",
        assignee: build(:user)
      }
    )
  end

  def completed_task_factory do
    now = DateTime.utc_now() |> DateTime.truncate(:second)

    struct!(
      task_factory(),
      %{
        status: "done",
        completed_at: now,
        assignee: build(:user)
      }
    )
  end

  # Helper functions for creating related records
  def user_with_account(attrs \\ %{}) do
    account = insert(:account)
    user = insert(:user, Map.put(attrs, :account, account))
    # Ensure account membership for tests
    _ = Codebake.Accounts.ensure_account_membership(user, account, "admin")
    user
  end

  def project_with_owner(attrs \\ %{}) do
    account = insert(:account)
    project = insert(:project, Map.put(attrs, :account, account))
    owner = insert(:user, account: account)
    # Ensure account membership for owner
    _ = Codebake.Accounts.ensure_account_membership(owner, account, "admin")
    insert(:owner_project_membership, project: project, user: owner, invited_by: owner)

    %{project: project, owner: owner, account: account}
  end

  def project_with_members(member_count \\ 3, attrs \\ %{}) do
    %{project: project, owner: owner, account: account} = project_with_owner(attrs)

    members =
      for _i <- 1..member_count do
        user = insert(:user, account: account)
        _ = Codebake.Accounts.ensure_account_membership(user, account, "admin")
        insert(:project_membership, project: project, user: user, invited_by: owner)
        user
      end

    %{project: project, owner: owner, members: members, account: account}
  end

  def project_with_tasks(task_count \\ 5, _attrs \\ %{}) do
    %{project: project, owner: owner} = project_with_owner()

    tasks =
      for i <- 1..task_count do
        insert(:task, project: project, created_by: owner, task_number: i)
      end

    %{project: project, tasks: tasks, owner: owner}
  end

  # Utility functions for sequences
  def unique_user_email, do: sequence(:unique_email, &"user#{&1}@example.com")
  def valid_user_password, do: "password123"
end
