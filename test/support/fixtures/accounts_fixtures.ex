defmodule Codebake.AccountsFixtures do
  @moduledoc """
  This module defines test helpers for creating
  entities via the `Codebake.Accounts` context.
  """

  import Ecto.Query

  alias <PERSON><PERSON><PERSON>.Accounts
  alias Codebake.Accounts.Scope

  def unique_user_email, do: "user#{System.unique_integer()}@example.com"
  def valid_user_password, do: "hello world!"

  def valid_user_attributes(attrs \\ %{}) do
    %{
      email: unique_user_email()
    }
    |> Map.merge(Enum.into(attrs, %{}))
  end

  def unconfirmed_user_fixture(attrs \\ %{}) do
    {:ok, user} =
      attrs
      |> valid_user_attributes()
      |> Accounts.register_user()

    user
  end

  def user_fixture(attrs \\ %{}) do
    user = unconfirmed_user_fixture(attrs)

    # Update user with account_id if provided, and create AccountMembership (admin)
    user =
      if Map.has_key?(attrs, :account_id) do
        {:ok, updated_user} = Accounts.update_user(user, %{account_id: attrs.account_id})
        # Ensure admin membership for tests
        account = Accounts.get_account!(attrs.account_id)
        _ = Accounts.ensure_account_membership(updated_user, account, "admin")
        updated_user
      else
        user
      end

    token =
      extract_user_token(fn url ->
        Accounts.deliver_login_instructions(user, url)
      end)

    {:ok, {user, _expired_tokens}} =
      Accounts.login_user_by_magic_link(token)

    user
  end

  def user_with_account_fixture(attrs \\ %{}) do
    # Create a proper account first
    account = account_fixture(%{name: "Test Company", slug: "test-company"})

    # Create user
    user = user_fixture(attrs)

    # Associate user with account and ensure membership
    {:ok, user} = Accounts.update_user(user, %{account_id: account.id})
    _ = Accounts.ensure_account_membership(user, account, "admin")

    user
  end

  def user_scope_fixture do
    user = user_fixture()
    user_scope_fixture(user)
  end

  def user_scope_fixture(user) do
    Scope.for_user(user)
  end

  def set_password(user) do
    {:ok, {user, _expired_tokens}} =
      Accounts.update_user_password(user, %{password: valid_user_password()})

    user
  end

  def extract_user_token(fun) do
    {:ok, captured_email} = fun.(&"[TOKEN]#{&1}[TOKEN]")
    [_, token | _] = String.split(captured_email.text_body, "[TOKEN]")
    token
  end

  def override_token_authenticated_at(token, authenticated_at) when is_binary(token) do
    Codebake.Repo.update_all(
      from(t in Accounts.UserToken,
        where: t.token == ^token
      ),
      set: [authenticated_at: authenticated_at]
    )
  end

  def generate_user_magic_link_token(user) do
    {encoded_token, user_token} = Accounts.UserToken.build_email_token(user, "login")
    Codebake.Repo.insert!(user_token)
    {encoded_token, user_token.token}
  end

  def offset_user_token(token, amount_to_add, unit) do
    dt = DateTime.add(DateTime.utc_now(:second), amount_to_add, unit)

    Codebake.Repo.update_all(
      from(ut in Accounts.UserToken, where: ut.token == ^token),
      set: [inserted_at: dt, authenticated_at: dt]
    )
  end

  def account_fixture(attrs \\ %{}) do
    unique_id = System.unique_integer([:positive]) |> rem(999)

    default_attrs = %{
      name: "Acme Corporation",
      slug: "acme-corp-#{unique_id}"
    }

    attrs = Map.merge(default_attrs, attrs)
    {:ok, account} = Accounts.create_account(attrs)
    account
  end
end
