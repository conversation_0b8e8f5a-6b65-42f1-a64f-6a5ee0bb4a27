// Kanban drag-and-drop hook for Codebake
// Manual registration pattern for LiveView hooks
// Usage in template: phx-hook="KanbanDnD"

export const KanbanDnD = {
  mounted() {
    // Use event delegation on the board root, so we don't have to rebind on patches.
    this._onDragStart = (e) => {
      const card = e.target && e.target.closest && e.target.closest('[data-task-id]')
      if (!card) return
      const taskId = card.dataset.taskId
      try {
        e.dataTransfer.effectAllowed = 'move'
        e.dataTransfer.setData('text/plain', taskId)
      } catch (_) {
        // Some browsers require setData to succeed for DnD, ignore failures
      }
      card.classList.add('opacity-60')
    }

    this._onDragEnd = (e) => {
      const card = e.target && e.target.closest && e.target.closest('[data-task-id]')
      if (card) card.classList.remove('opacity-60')
      // Clean up any lingering highlights
      this._clearDropHighlights()
    }

    this._onDragOver = (e) => {
      const zone = this._getDropzone(e.target)
      if (!zone) return
      e.preventDefault() // Allow drop
      try { e.dataTransfer.dropEffect = 'move' } catch (_) {}
    }

    this._onDragEnter = (e) => {
      const zone = this._getDropzone(e.target)
      if (!zone) return
      zone.classList.add('ring', 'ring-primary/60', 'ring-offset-2', 'ring-offset-base-200')
    }

    this._onDragLeave = (e) => {
      const zone = this._getDropzone(e.target)
      if (!zone) return
      // Only remove if leaving the zone entirely
      if (!zone.contains(e.relatedTarget)) {
        zone.classList.remove('ring', 'ring-primary/60', 'ring-offset-2', 'ring-offset-base-200')
      }
    }

    this._onDrop = (e) => {
      const zone = this._getDropzone(e.target)
      if (!zone) return
      e.preventDefault()

      const toStatus = zone.dataset.status
      let taskId = null
      try { taskId = e.dataTransfer.getData('text/plain') } catch (_) {}

      // Fallback: pull from currently dragging card if dataTransfer failed
      if (!taskId) {
        const dragging = this.el.querySelector('[data-task-id].opacity-60')
        taskId = dragging && dragging.dataset.taskId
      }

      // Clean up visual highlights
      this._clearDropHighlights()

      if (!toStatus || !taskId) return

      // Determine before_id (the card we are dropping before) within the dropzone
      const cards = Array.from(zone.querySelectorAll('[data-task-id]'))
        .filter((el) => el.dataset.taskId !== taskId)

      let beforeId = null
      const y = e.clientY
      for (const el of cards) {
        const r = el.getBoundingClientRect()
        const midY = r.top + r.height / 2
        if (y < midY) { beforeId = el.dataset.taskId; break }
      }

      // Push payload to server; server validates and re-streams affected lists
      this.pushEvent('move_task', { task_id: taskId, new_status: toStatus, before_id: beforeId })
      // Counters may need to change visibility after re-render
      setTimeout(() => this._recomputeCounters(), 0)
    }

    // Helper: find nearest dropzone element
    this._getDropzone = (el) => el && el.closest && el.closest('[data-dropzone="true"]')

    this._clearDropHighlights = () => {
      this.el.querySelectorAll('[data-dropzone="true"]').forEach((z) =>
        z.classList.remove('ring', 'ring-primary/60', 'ring-offset-2', 'ring-offset-base-200')
      )
    }

    // Counter visibility recompute, used across events
    this._recomputeCounters = () => {
      const zones = this.el.querySelectorAll('[data-dropzone="true"]')
      zones.forEach((zone) => {
        const list = zone.querySelector('[role="list"]')
        const badge = zone.querySelector('[data-count]')
        if (!list || !badge) return
        const overflow = list.scrollHeight > list.clientHeight + 1
        zone.dataset.overflow = overflow ? 'true' : 'false'
        if (overflow) {
          badge.classList.remove('hidden')
        } else {
          badge.classList.add('hidden')
        }
      })
    }

    // Bind listeners
    this.el.addEventListener('dragstart', this._onDragStart)
    this.el.addEventListener('dragend', this._onDragEnd)
    this.el.addEventListener('dragover', this._onDragOver)
    this.el.addEventListener('dragenter', this._onDragEnter)
    this.el.addEventListener('dragleave', this._onDragLeave)
    this.el.addEventListener('drop', this._onDrop)

    // Recompute counters after mount and on resize
    setTimeout(() => this._recomputeCounters(), 0)
    this._onResize = () => this._recomputeCounters()
    window.addEventListener('resize', this._onResize)
  },

  updated() {
    // After DOM patches, recompute counter visibility
    this._recomputeCounters()
  },

  destroyed() {
    // Unbind listeners to avoid leaks when LiveView is removed.
    this.el.removeEventListener('dragstart', this._onDragStart)
    this.el.removeEventListener('dragend', this._onDragEnd)
    this.el.removeEventListener('dragover', this._onDragOver)
    this.el.removeEventListener('dragenter', this._onDragEnter)
    this.el.removeEventListener('dragleave', this._onDragLeave)
    this.el.removeEventListener('drop', this._onDrop)
    window.removeEventListener('resize', this._onResize)
  }
}

