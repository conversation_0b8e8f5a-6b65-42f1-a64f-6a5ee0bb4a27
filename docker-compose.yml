services:
  # Database Seeder (run with: docker-compose --profile tools up seeder)
  seeder:
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_started
    environment:
      DATABASE_URL: ecto://postgres:postgres@postgres/codebake_dev
      DB_HOSTNAME: postgres
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: codebake_dev
      MIX_ENV: "dev"
      # S3/LocalStack configuration
      S3_HOST: localstack
      S3_PORT: "4566"
    volumes:
      - .:/app
      - build:/app/_build
      - deps:/app/deps
    command: >
      bash -c "
        echo 'Cleaning and installing dependencies...'
        mix deps.clean --all
        mix deps.get --only dev
        echo 'Setting up database...'
        mix ecto.create --quiet
        mix ecto.migrate --quiet
        echo 'Running seeds...'
        mix run priv/repo/seeds.exs
        echo 'Seeding completed successfully!'
      "
    profiles:
      - tools

  # Database Reset (run with: docker-compose --profile tools up db-reset)
  db-reset:
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_started
    environment:
      DATABASE_URL: ecto://postgres:postgres@postgres/codebake_dev
      DB_HOSTNAME: postgres
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: codebake_dev
      MIX_ENV: "dev"
      # S3/LocalStack configuration
      S3_HOST: localstack
      S3_PORT: "4566"
    volumes:
      - .:/app
      - build:/app/_build
      - deps:/app/deps
    command: >
      bash -c "
        mix deps.get
        echo 'Terminating all connections to the database...'
        PGPASSWORD=postgres psql -h postgres -U postgres -c \"SELECT pg_terminate_backend(pg_stat_activity.pid) FROM pg_stat_activity WHERE pg_stat_activity.datname = 'codebake_dev' AND pid <> pg_backend_pid();\"
        echo 'Resetting database...'
        mix ecto.reset
        echo 'Database reset complete!'
      "
    profiles:
      - tools

  # PostgreSQL Monitor (run with: docker-compose --profile tools up pg-monitor)
  pg-monitor:
    image: bash:latest
    container_name: codebake_pg_monitor
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./scripts:/scripts
    command: /scripts/pg_monitor.sh
    profiles:
      - tools

  # ERD Generator (run with: docker-compose --profile tools up erd)
  erd:
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      postgres:
        condition: service_healthy
    environment:
      DATABASE_URL: ecto://postgres:postgres@postgres/codebake_dev
      DB_HOSTNAME: postgres
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: codebake_dev
      MIX_ENV: "dev"
    volumes:
      - .:/app
      - build:/app/_build
      - deps:/app/deps
    command: bash -c "mix deps.get && mix ecto.migrate.erd"
    profiles:
      - tools

  # Test Runner (run with: docker-compose --profile tools up test)
  test:
    build:
      context: .
      dockerfile: Dockerfile.dev
    depends_on:
      postgres-test:
        condition: service_healthy
      localstack:
        condition: service_healthy
    environment:
      # Database configuration - use the service name as the hostname
      DATABASE_URL: ecto://postgres:postgres@postgres-test/codebake_test
      TEST_DB_HOSTNAME: postgres-test
      TEST_DB_USERNAME: postgres
      TEST_DB_PASSWORD: postgres
      TEST_DB_DATABASE: codebake_test
      # Test-specific configuration
      MIX_ENV: "test"
      # AWS configuration for LocalStack
      AWS_ACCESS_KEY_ID: test
      AWS_SECRET_ACCESS_KEY: test
      AWS_REGION: us-east-1
      AWS_ENDPOINT: http://localstack:4566
      # Use the service name instead of localhost for S3 host
      S3_HOST: localstack
      S3_PORT: 4566
      # Set the bucket name
      S3_BUCKET: codebake-attachments-test
      # Set Elixir VM options for better performance
      ERL_AFLAGS: "-kernel shell_history enabled"
      ELIXIR_ERL_OPTIONS: "+K true +A 128 +P 1000000"
      # Set Docker environment variable
      DOCKER: "true"
      # Force lazy_html to compile from source instead of using precompiled binaries
      ELIXIR_MAKE_CACHE_DIR: ""
    volumes:
      - .:/app
      - build_test:/app/_build
      - deps:/app/deps
    # Add resource limits to ensure the container has enough resources
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
    command: >
      bash -c "
        mix deps.get
        # Setup test database with migrations
        mix ecto.create --quiet
        mix ecto.migrate --quiet
        # Run the tests with increased timeouts
        mix test --trace --timeout 120000 --color
      "
    profiles:
      - tools
  # PostgreSQL Database
  postgres:
    image: postgres:17
    container_name: codebake_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: codebake_dev
      # PostgreSQL performance tuning
      POSTGRES_INITDB_ARGS: "--data-checksums"
      POSTGRES_HOST_AUTH_METHOD: "trust"
      # Connection settings
      PGDATA: "/var/lib/postgresql/data"
      # Listen on all interfaces
      POSTGRES_LISTEN_ADDRESSES: "*"
      # Memory allocation
      POSTGRES_SHARED_BUFFERS: "1GB"
      POSTGRES_EFFECTIVE_CACHE_SIZE: "1.5GB"
      POSTGRES_WORK_MEM: "32MB"
      POSTGRES_MAINTENANCE_WORK_MEM: "256MB"
      # Connection handling
      POSTGRES_MAX_CONNECTIONS: "100"
      POSTGRES_DEFAULT_STATISTICS_TARGET: "100"
      # Write-ahead log settings - Minimized for development/testing
      POSTGRES_WAL_LEVEL: "minimal"
      POSTGRES_MAX_WAL_SENDERS: "0"
      POSTGRES_FSYNC: "off"
      POSTGRES_SYNCHRONOUS_COMMIT: "off"
      POSTGRES_FULL_PAGE_WRITES: "off"
      POSTGRES_WAL_BUFFERS: "16MB"
      POSTGRES_WAL_WRITER_DELAY: "1s"
      POSTGRES_COMMIT_DELAY: "0"
      POSTGRES_WAL_SKIP_THRESHOLD: "8MB"
      # Query optimization
      POSTGRES_RANDOM_PAGE_COST: "1.1"
      POSTGRES_EFFECTIVE_IO_CONCURRENCY: "200"
      POSTGRES_JIT: "on"
      # Autovacuum settings - Reduced frequency for development/testing
      POSTGRES_AUTOVACUUM: "on"
      POSTGRES_AUTOVACUUM_MAX_WORKERS: "2"
      POSTGRES_AUTOVACUUM_NAPTIME: "1h"
      POSTGRES_AUTOVACUUM_VACUUM_SCALE_FACTOR: "0.2"
      POSTGRES_AUTOVACUUM_ANALYZE_SCALE_FACTOR: "0.1"
      POSTGRES_AUTOVACUUM_VACUUM_COST_DELAY: "20"
      POSTGRES_AUTOVACUUM_VACUUM_COST_LIMIT: "1000"
      # Checkpoint settings - Minimized for development/testing
      POSTGRES_CHECKPOINT_TIMEOUT: "15min"
      POSTGRES_CHECKPOINT_COMPLETION_TARGET: "0.9"
      POSTGRES_MAX_WAL_SIZE: "2GB"
      POSTGRES_MIN_WAL_SIZE: "80MB"
      POSTGRES_CHECKPOINT_WARNING: "0"
    ports:
      - "15432:5432"  # codebake project PostgreSQL dev
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./pg_hba/pg_hba.conf:/etc/postgresql/pg_hba.conf

    command: ["postgres", "-c", "hba_file=/etc/postgresql/pg_hba.conf", "-c", "listen_addresses=*"]
    # Add resource limits to ensure the container has enough resources
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          memory: 1.5G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL Database - Ultra-optimized for Testing
  postgres-test:
    image: postgres:17
    container_name: codebake_postgres_test
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: codebake_test
      # Disable data checksums for speed (default is no checksums anyway)
      POSTGRES_INITDB_ARGS: ""
      POSTGRES_HOST_AUTH_METHOD: "trust"
      PGDATA: "/var/lib/postgresql/data"
    ports:
      - "15433:5432"  # codebake project PostgreSQL test
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./postgres-test.conf:/etc/postgresql/postgresql.conf
      - ./pg_hba.conf:/etc/postgresql/pg_hba.conf
    command: ["postgres", "-c", "config_file=/etc/postgresql/postgresql.conf", "-c", "listen_addresses=*", "-c", "hba_file=/etc/postgresql/pg_hba.conf"]
    deploy:
      resources:
        limits:
          cpus: '2'
          memory: 2G
        reservations:
          memory: 1.5G
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 3s
      retries: 3
    profiles:
      - tools

  # LocalStack - S3 Service Emulator
  localstack:
    image: localstack/localstack:latest
    container_name: codebake_localstack
    ports:
      - "14566:4566"  # codebake project LocalStack S3
    environment:
      - SERVICES=s3
      - AWS_ACCESS_KEY_ID=test
      - AWS_SECRET_ACCESS_KEY=test
      - CORS_ALLOW_ORIGIN=*
      - CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS
      - CORS_ALLOW_HEADERS=*
      # Performance and stability improvements
      - DEBUG=1
      - PERSISTENCE=1
      - LAMBDA_EXECUTOR=local
      - DOCKER_HOST=unix:///var/run/docker.sock
      # Memory and resource management
      - LOCALSTACK_HOST=localstack
      - EDGE_PORT=4566
      # Disable unnecessary features for better performance
      - DISABLE_CORS_CHECKS=1
      - SKIP_INFRA_DOWNLOADS=1
    volumes:
      - ./localstack:/var/lib/localstack
      - /var/run/docker.sock:/var/run/docker.sock
    # Add resource limits to prevent memory issues
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ["CMD", "bash", "-c", "curl -f http://localhost:4566/_localstack/health | grep -qE '\"s3\": \"(running|available)\"' && curl -f http://localhost:4566/"]
      interval: 15s
      timeout: 10s
      retries: 12
      start_period: 45s

  # Elixir Application (Development Mode)
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: codebake_app
    depends_on:
      postgres:
        condition: service_healthy
      localstack:
        condition: service_healthy
    environment:
      # Database configuration - use the service name as the hostname
      DATABASE_URL: ecto://postgres:postgres@postgres/codebake_dev
      DB_HOSTNAME: postgres
      DB_USERNAME: postgres
      DB_PASSWORD: postgres
      DB_DATABASE: codebake_dev
      # Phoenix configuration
      PHX_HOST: localhost
      PORT: 4000
      SECRET_KEY_BASE: scfVC45gvnanytomEQG7f6vWlKV8gV6e4dDnwjfGBl2wFKxrJDs15R5vYxPpG2wP
      # Enable server
      PHX_SERVER: "true"
      # Explicitly set MIX_ENV
      MIX_ENV: "dev"
      # Disable type checking to work around OTP 28 compatibility issue
      ERL_COMPILER_OPTIONS: "no_type_opt"
      # Disable parallel type checking
      ELIXIR_TYPE_CHECK: "false"
      # AWS configuration for LocalStack
      AWS_ACCESS_KEY_ID: test
      AWS_SECRET_ACCESS_KEY: test
      AWS_REGION: us-east-1
      AWS_ENDPOINT: http://localstack:4566
      # S3 configuration for development
      S3_HOST: localstack
      S3_PORT: 4566
      S3_BUCKET: codebake-attachments-dev
      # External S3 URL for browser uploads (accessible from host)
      S3_EXTERNAL_HOST: localhost
      S3_EXTERNAL_PORT: 14566
      # Mark we are inside Docker so dev.exs binds to 0.0.0.0
      DOCKER: "true"
    ports:
      - "4000:4000"  # codebake project Phoenix server
    volumes:
      - .:/app
      - build:/app/_build
      - deps:/app/deps
      - node_modules:/app/assets/node_modules
    command: >
      bash -c "
        echo 'Waiting for LocalStack S3 to be fully ready...'
        sleep 5
        echo 'Installing dependencies...'
        mix deps.get
        echo 'Setting up database...'
        mix ecto.create
        mix ecto.migrate
        echo 'Running seeds...'
        mix run priv/repo/seeds.exs
        echo 'Starting Phoenix server...'
        mix phx.server
      "

volumes:
  postgres_data:
  postgres_test_data:
  build:
  build_test:
  deps:
  node_modules:
