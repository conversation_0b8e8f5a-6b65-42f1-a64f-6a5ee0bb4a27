defmodule CodebakeWeb.Authorization do
  @moduledoc """
  Authorization helpers for CodebakeWeb controllers and LiveViews.

  This module provides a convenient way to add authorization to your
  controllers and LiveViews by importing common authorization patterns.

  ## Usage

      defmodule MyController do
        use CodebakeWeb, :controller
        import CodebakeWeb.Authorization

        def show(conn, %{"project_id" => project_id}) do
          with {:ok, project} <- load_and_authorize_project(conn, project_id, :read_project) do
            render(conn, :show, project: project)
          end
        end
      end

      defmodule MyLive do
        use CodebakeWeb, :live_view
        import CodebakeWeb.Authorization

        def mount(%{"project_id" => project_id}, _session, socket) do
          case load_and_authorize_project_lv(socket, project_id, :read_project) do
            {:ok, project, socket} ->
              {:ok, assign(socket, :project, project)}
            {:error, socket} ->
              {:ok, socket}
          end
        end
      end
  """

  import Phoenix.LiveView, only: [put_flash: 3]

  alias Phoenix.LiveView

  alias <PERSON>bake.{Accounts, Authorization, Projects}

  ## Controller helpers

  ## LiveView helpers

  ## Permission checking helpers

  @doc """
  Check if current user can perform action on a resource (e.g., project).
  """
  def can?(scope_or_conn_or_socket, action, team) do
    scope = extract_scope(scope_or_conn_or_socket)
    Authorization.can?(scope, action, team)
  end

  @doc """
  Get current user from various contexts.
  """
  def current_user(scope_or_conn_or_socket) do
    scope = extract_scope(scope_or_conn_or_socket)

    case scope do
      %{user: user} -> user
      _ -> nil
    end
  end

  @doc """
  Check if user is authenticated.
  """
  def authenticated?(scope_or_conn_or_socket) do
    current_user(scope_or_conn_or_socket) != nil
  end

  ## Project authorization helpers

  @doc """
  Load and authorize a project in a LiveView.

  Returns `{:ok, project, socket}` if successful, `{:error, socket}` if not.
  The error socket will have a flash message and redirect set.
  """
  def load_and_authorize_project_lv(socket, project_id, action, redirect_path \\ "/") do
    case Projects.get_project(project_id) |> Codebake.Repo.preload(:account) do
      nil ->
        socket =
          socket
          |> put_flash(:error, "Project not found")
          |> LiveView.redirect(to: redirect_path)

        {:error, socket}

      project ->
        case Bodyguard.permit(Authorization, action, socket.assigns.current_scope, project) do
          :ok ->
            {:ok, project, socket}

          {:error, :unauthorized} ->
            socket =
              socket
              |> put_flash(:error, "You don't have permission to access this project")
              |> LiveView.redirect(to: redirect_path)

            {:error, socket}
        end
    end
  end

  @doc """
  Check if current user can access project (boolean check for LiveViews).
  """
  def can_access_project?(scope_or_conn_or_socket, action, project) do
    scope = extract_scope(scope_or_conn_or_socket)
    Authorization.can?(scope, action, project)
  end

  @doc """
  Get user's role in a project.
  """
  def user_role_in_project(scope_or_conn_or_socket, project) do
    scope = extract_scope(scope_or_conn_or_socket)

    case scope do
      %{user: user} -> Projects.get_user_role_in_project(user, project)
      _ -> nil
    end
  end

  @doc """
  Check if user has minimum role level in project.
  """
  def has_min_project_role?(scope_or_conn_or_socket, project, min_role) do
    scope = extract_scope(scope_or_conn_or_socket)

    case scope do
      %{user: user} -> Authorization.has_min_project_role?(user, project, min_role)
      _ -> false
    end
  end

  @doc "Check if user can manage project (admin or above)"
  def can_manage_project?(scope_or_conn_or_socket, project) do
    has_min_project_role?(scope_or_conn_or_socket, project, "admin")
  end

  @doc "Check if user can contribute to project (member or above)"
  def can_contribute_to_project?(scope_or_conn_or_socket, project) do
    has_min_project_role?(scope_or_conn_or_socket, project, "member")
  end

  ## Account authorization helpers

  @doc """
  Load and authorize an account in a LiveView.

  Returns `{:ok, account, socket}` if successful, `{:error, socket}` if not.
  The error socket will have a flash message and redirect set.
  """
  def load_and_authorize_account_lv(socket, account_slug, _action, redirect_path \\ "/") do
    case Accounts.get_account_by_slug(account_slug) do
      nil ->
        socket =
          socket
          |> put_flash(:error, "Account not found")
          |> LiveView.redirect(to: redirect_path)

        {:error, socket}

      account ->
        user = current_user(socket)

        # Allow access to account-level pages for any team member of the account.
        if Accounts.account_member?(user, account) do
          {:ok, account, socket}
        else
          socket =
            socket
            |> put_flash(:error, "You don't have permission to access this account")
            |> LiveView.redirect(to: redirect_path)

          {:error, socket}
        end
    end
  end

  ## Private helpers

  defp extract_scope(%{assigns: %{current_scope: scope}}), do: scope
  defp extract_scope(%{current_scope: scope}), do: scope
  defp extract_scope(scope), do: scope
end
