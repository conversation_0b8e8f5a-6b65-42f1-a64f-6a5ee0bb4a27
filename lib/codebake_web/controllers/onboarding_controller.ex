defmodule CodebakeWeb.OnboardingController do
  use <PERSON><PERSON>ke<PERSON>eb, :controller

  alias <PERSON><PERSON><PERSON>.Accounts

  def setup_account(conn, %{"account" => account_params}) do
    user = conn.assigns.current_scope.user

    # Check if user has an existing account to update
    existing_account = Accounts.get_user_account(user)

    if existing_account do
      # Update existing account
      case Accounts.update_account(existing_account, account_params) do
        {:ok, account} ->
          # Ensure creator is an Admin of the account
          _ = Accounts.ensure_account_membership(user, account, "owner")

          conn
          |> put_flash(:info, "Account updated successfully! Welcome to #{account.name}!")
          |> redirect(to: ~p"/users/settings")

        {:error, changeset} ->
          # Extract errors and show them to the user
          errors = 
            changeset.errors
            |> Enum.map(fn {field, {message, _}} -> "#{field} #{message}" end)
            |> Enum.join(", ")

          conn
          |> put_flash(:error, "Account update failed: #{errors}")
          |> redirect(to: ~p"/onboarding/account")
      end
    else
      # Create new account
      case Accounts.create_account(account_params) do
        {:ok, account} ->
          # Update user to belong to this account
          case Accounts.update_user(user, %{account_id: account.id}) do
            {:ok, _user} ->
              # Ensure creator is an Admin of the account
              _ = Accounts.ensure_account_membership(user, account, "owner")

              conn
              |> put_flash(:info, "Account created successfully! Welcome to #{account.name}!")
              |> redirect(to: ~p"/users/settings")

            {:error, _changeset} ->
              # Clean up the account if user update fails
              Accounts.delete_account(account)

              conn
              |> put_flash(:error, "Failed to associate user with account")
              |> redirect(to: ~p"/onboarding/account")
          end

        {:error, changeset} ->
          # Extract errors and show them to the user
          errors = 
            changeset.errors
            |> Enum.map(fn {field, {message, _}} -> "#{field} #{message}" end)
            |> Enum.join(", ")

          conn
          |> put_flash(:error, "Account creation failed: #{errors}")
          |> redirect(to: ~p"/onboarding/account")
      end
    end
  end
end
