defmodule CodebakeWeb.PageController do
  use <PERSON>bake<PERSON>eb, :controller

  alias <PERSON><PERSON><PERSON>.{Accounts, Projects}

  def home(conn, _params) do
    case conn.assigns.current_scope do
      # User is not authenticated - redirect to login
      nil ->
        redirect(conn, to: ~p"/users/log-in")

      # User is authenticated - check account setup first
      %{user: user} when not is_nil(user) ->
        if Accounts.user_needs_account_setup?(user) do
          redirect(conn, to: ~p"/onboarding/account")
        else
          # Get user's projects to find their accounts
          user_projects = Projects.list_user_projects(user)

          case user_projects do
            # User has no projects - redirect to account dashboard
            [] ->
              # Get user's account (assuming they have one from onboarding)
              case Accounts.get_user_account(user) do
                nil -> redirect(conn, to: ~p"/onboarding/account")
                account -> redirect(conn, to: ~p"/#{account.slug}")
              end

            # User has projects - redirect to first project's account dashboard
            [first_project | _] ->
              redirect(conn, to: ~p"/#{first_project.account.slug}")
          end
        end

      # Fallback case - redirect to login
      _ ->
        redirect(conn, to: ~p"/users/log-in")
    end
  end
end
