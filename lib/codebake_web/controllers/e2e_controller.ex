defmodule CodebakeWeb.E2EController do
  @moduledoc """
  Test-only endpoints to bootstrap authenticated flows for E2E/browser tests.

  These routes are only wired when dev routes are enabled.
  """

  use CodebakeWeb, :controller

  alias <PERSON><PERSON><PERSON>.{Accounts, Projects}

  @doc """
  Creates a throwaway account, user, and project, logs the user in, and
  redirects to the project's board page.
  """
  def bootstrap(conn, params) do
    # Create an account
    account_name =
      Map.get(params, "account_name", "E2E Account #{System.unique_integer([:positive])}")

    {:ok, account} =
      Accounts.create_account(%{
        name: account_name,
        billing_email: Map.get(params, "billing_email", "<EMAIL>"),
        subscription_tier: "freemium"
      })

    # Create a user and associate with account as admin
    email = Map.get(params, "email", "e2e-#{System.unique_integer([:positive])}@example.com")
    {:ok, user} = Accounts.register_user(%{email: email})
    {:ok, user} = Accounts.update_user(user, %{account_id: account.id})
    _ = Accounts.ensure_account_membership(user, account, "admin")

    # Create a project owned by the user
    project_name =
      Map.get(params, "project_name", "E2E Project #{System.unique_integer([:positive])}")

    task_prefix = Map.get(params, "task_prefix", "E2E")

    {:ok, project} =
      Projects.create_project(account, user, %{
        "name" => project_name,
        "task_prefix" => task_prefix
      })

    # Log in the user by setting the session token
    token = Accounts.generate_user_session_token(user)

    conn
    |> put_session(:user_token, token)
    |> redirect(to: ~p"/#{account.slug}/projects/#{project.id}/board")
  end
end
