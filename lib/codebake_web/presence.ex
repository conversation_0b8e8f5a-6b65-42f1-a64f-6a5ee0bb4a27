defmodule CodebakeWeb.Presence do
  @moduledoc """
  Provides presence tracking for users across the application.

  Used to track which users are currently viewing project boards,
  account dashboards, and other collaborative features.
  """

  use Phoenix.Presence,
    otp_app: :codebake,
    pubsub_server: Codebake.PubSub

  @doc """
  Returns a list of users currently present in a given topic.
  """
  def list_users(topic) do
    list(topic)
    |> Enum.map(fn {_user_id, %{metas: [meta | _]}} ->
      meta.user
    end)
  end

  @doc """
  Returns the count of users currently present in a given topic.
  """
  def user_count(topic) do
    list(topic) |> map_size()
  end

  @doc """
  Tracks a user's presence in a topic with additional metadata.
  """
  def track_user(pid, topic, user_id, user_data) do
    track(pid, topic, user_id, %{
      user: user_data,
      joined_at: System.system_time(:second)
    })
  end
end
