defmodule CodebakeWeb.Router do
  use CodebakeWeb, :router

  import CodebakeWeb.UserAuth

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {CodebakeWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
    plug :fetch_current_scope_for_user
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  scope "/", CodebakeWeb do
    pipe_through :browser

    get "/", PageController, :home
  end

  # Other scopes may use custom stacks.
  # scope "/api", CodebakeWeb do
  #   pipe_through :api
  # end

  # Enable LiveDashboard and Swoosh mailbox preview in development
  if Application.compile_env(:codebake, :dev_routes) do
    # If you want to use the LiveDashboard in production, you should put
    # it behind authentication and allow only admins to access it.
    # If your application does not have an admins-only section yet,
    # you can use Plug.BasicAuth to set up some basic authentication
    # as long as you are also using SSL (which you should anyway).
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: CodebakeWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end

    # Test/E2E helper routes (dev-only)
    scope "/e2e", CodebakeWeb do
      pipe_through :browser

      get "/bootstrap", E2EController, :bootstrap
    end
  end

  ## Authentication routes

  scope "/", CodebakeWeb do
    pipe_through [:browser]

    live_session :current_user,
      on_mount: [{CodebakeWeb.UserAuth, :mount_current_scope}] do
      live "/users/register", UserLive.Registration, :new
      live "/users/log-in", UserLive.Login, :new
      live "/users/log-in/:token", UserLive.Confirmation, :new
    end

    post "/users/log-in", UserSessionController, :create
    post "/users/register", UserSessionController, :register
    delete "/users/log-out", UserSessionController, :delete
  end

  scope "/", CodebakeWeb do
    pipe_through [:browser, :require_authenticated_user]

    # Onboarding routes (no account setup check)
    live_session :onboarding,
      on_mount: [{CodebakeWeb.UserAuth, :require_authenticated}] do
      live "/onboarding/account", OnboardingLive.Account, :new
    end

    # User settings routes (no account setup check needed)
    live_session :user_settings,
      on_mount: [{CodebakeWeb.UserAuth, :require_authenticated}] do
      live "/users/settings", UserLive.Settings, :edit
      live "/users/settings/confirm-email/:token", UserLive.Settings, :confirm_email
      live "/users/settings/tokens", UserLive.PersonalAccessTokens, :index
    end

    # Main app routes (with account setup check)
    live_session :require_authenticated_user,
      on_mount: [{CodebakeWeb.UserAuth, :require_account_setup}] do
      # Account management routes
      live "/accounts/new", AccountLive.New, :new

      # Account dashboard
      live "/:account_slug", AccountLive.Dashboard, :show

      # Project management routes with account slugs
      live "/:account_slug/projects", ProjectLive.Index, :index
      live "/:account_slug/projects/new", ProjectLive.Index, :new
      live "/:account_slug/projects/:project_id", ProjectLive.Board, :show
      live "/:account_slug/projects/:project_id/board", ProjectLive.Board, :show
    end

    post "/users/update-password", UserSessionController, :update_password
  end
end
