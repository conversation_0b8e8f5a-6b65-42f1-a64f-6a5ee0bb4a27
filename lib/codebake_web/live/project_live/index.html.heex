<Layouts.app flash={@flash} current_scope={@current_scope}>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-4">
            <.link
              navigate={~p"/#{@account.slug}"}
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <.icon name="hero-arrow-left" class="w-5 h-5" />
            </.link>
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Projects</h1>
              <p class="text-sm text-gray-500 dark:text-gray-400">{@account.name}</p>
            </div>
          </div>

          <%= if @can_manage do %>
            <.link
              patch={~p"/#{@account.slug}/projects/new"}
              class="btn btn-primary"
            >
              <.icon name="hero-plus" class="w-4 h-4 mr-2" /> New Project
            </.link>
          <% end %>
        </div>
      </div>
    </div>
    
<!-- Projects Grid -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <%= if Enum.empty?(@projects_with_roles) do %>
        <!-- Empty State -->
        <div class="text-center py-12">
          <.icon
            name="hero-folder"
            class="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4"
          />
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No projects yet</h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">
            Get started by creating your first project.
          </p>
          <%= if @can_manage do %>
            <.link
              patch={~p"/#{@account.slug}/projects/new"}
              class="btn btn-primary"
            >
              <.icon name="hero-plus" class="w-4 h-4 mr-2" /> Create Project
            </.link>
          <% end %>
        </div>
      <% else %>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <%= for %{project: project} <- @projects_with_roles do %>
            <div class="card bg-base-100 border border-base-300 shadow-sm hover:shadow-md transition-shadow">
              <div class="card-body">
                <div class="flex justify-between items-start mb-4">
                  <h3 class="card-title text-lg font-semibold truncate">
                    {project.name}
                  </h3>
                  <span class={["badge", project_status_color(project.status)]}>
                    {String.replace(project.status, "_", " ") |> String.capitalize()}
                  </span>
                </div>

                <%= if project.description do %>
                  <p class="text-base-content/70 text-sm mb-4 line-clamp-2">
                    {project.description}
                  </p>
                <% end %>
                
<!-- Project Stats -->
                <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                  <div class="flex items-center space-x-4">
                    <span class="flex items-center">
                      <.icon name="hero-hashtag" class="w-4 h-4 mr-1" />
                      {project.task_prefix}
                    </span>
                    <span class="flex items-center">
                      <.icon name="hero-list-bullet" class="w-4 h-4 mr-1" />
                      {project_task_count(project)} tasks
                    </span>
                  </div>
                </div>
                
<!-- Project Actions -->
                <div class="card-actions justify-between items-center">
                  <.link
                    navigate={~p"/#{@account.slug}/projects/#{project.id}"}
                    class="btn btn-outline btn-sm"
                  >
                    <.icon name="hero-eye" class="w-4 h-4 mr-2" /> View Board
                  </.link>

                  <%= if @can_manage do %>
                    <button
                      phx-click="delete_project"
                      phx-value-project-id={project.id}
                      data-confirm="Are you sure you want to delete this project? This action cannot be undone."
                      class="btn btn-ghost btn-sm text-error"
                    >
                      <.icon name="hero-trash" class="w-4 h-4" />
                    </button>
                  <% end %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
    
<!-- Create Project Modal -->
    <%= if @show_create_form do %>
      <div class="modal modal-open">
        <div class="modal-box w-11/12 max-w-md" phx-click-away="hide_create_form">
          <h3 class="font-bold text-lg">Create New Project</h3>

          <.form
            for={@form}
            phx-submit="create_project"
            phx-change="validate_project"
            class="space-y-4"
          >
            <div>
              <.input
                field={@form[:name]}
                type="text"
                label="Project Name"
                placeholder="Enter project name..."
                required
              />
            </div>

            <div>
              <.input
                field={@form[:description]}
                type="textarea"
                label="Description"
                placeholder="Describe the project..."
                rows="3"
              />
            </div>

            <div>
              <.input
                field={@form[:task_prefix]}
                type="text"
                label="Task Prefix"
                placeholder="e.g., PROJ, FEAT, BUG"
              />
              <p class="mt-1 text-sm text-base-content/70">
                Used for task identifiers (e.g., PROJ-1, PROJ-2)
              </p>
            </div>

            <div class="modal-action">
              <.link patch={~p"/#{@account.slug}/projects"} class="btn btn-ghost">
                Cancel
              </.link>
              <button type="submit" class="btn btn-primary">Create Project</button>
            </div>
          </.form>
        </div>
      </div>
    <% end %>
  </div>
</Layouts.app>
