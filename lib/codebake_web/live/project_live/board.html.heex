<Layouts.app flash={@flash} current_scope={@current_scope}>
  <div class="min-h-screen bg-base-200">
    <!-- Header -->
    <div class="bg-base-100 shadow-sm border-b border-base-300">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-2">
          <div class="flex items-center space-x-4">
            <.link
              navigate={~p"/#{@project.account.slug}"}
              class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <.icon name="hero-arrow-left" class="w-5 h-5" />
            </.link>
            <div>
              <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                Project: {@project.name}
              </h1>
            </div>
          </div>

          <div class="flex items-center space-x-3">
            <span class="text-sm text-gray-500 dark:text-gray-400">Phase:</span>
            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-sm">
                {if @current_phase, do: @current_phase["name"], else: "No phase"}
                <.icon name="hero-chevron-down" class="w-4 h-4 ml-1" />
              </div>
              <ul
                tabindex="0"
                class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-64"
              >
                <%= if @phases == [] do %>
                  <li class="menu-title"><span>No phases yet</span></li>
                <% else %>
                  <li :for={phase <- @phases}>
                    <button
                      phx-click="set_current_phase"
                      phx-value-phase_id={phase["id"]}
                      class={[
                        "justify-between",
                        @current_phase && @current_phase["id"] == phase["id"] && "active"
                      ]}
                    >
                      {phase["name"]}
                      <%= if @current_phase && @current_phase["id"] == phase["id"] do %>
                        <span class="badge badge-ghost">Current</span>
                      <% end %>
                    </button>
                  </li>
                <% end %>
                <%= if @can_manage do %>
                  <li class="menu-title"><span>Add new phase</span></li>
                  <li class="px-2 py-2">
                    <.form
                      for={@phase_form}
                      id="phase-form"
                      phx-submit="create_phase"
                      class="space-y-2"
                    >
                      <.input field={@phase_form[:name]} type="text" placeholder="Phase name" />
                      <button type="submit" class="btn btn-xs btn-primary">Add</button>
                    </.form>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- Kanban Board -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        id="kanban-board"
        phx-hook="KanbanDnD"
      >
        <!-- TODO Column -->
        <div
          class={[
            "bg-base-100 rounded-box shadow-sm min-h-96",
            status_color("todo")
          ]}
          data-status="todo"
          id="column-todo"
          data-dropzone="true"
          aria-label="To Do column"
        >
          <div class="p-3 border-b border-base-300">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">To Do</h3>
              <div class="flex items-center space-x-2">
                <span class="badge badge-ghost column-count" data-count>
                  {@counts["todo"] || 0}
                </span>
                <%= if @can_contribute do %>
                  <button
                    phx-click="show_task_form"
                    phx-value-status="todo"
                    class="btn btn-primary btn-circle btn-xs"
                  >
                    <.icon name="hero-plus" class="w-3 h-3" />
                  </button>
                <% end %>
              </div>
            </div>
          </div>
          <div class="p-2 space-y-2" id="tasks-todo" role="list" phx-update="stream">
            <div id="empty-todo" class="hidden only:block text-center py-6">
              <.icon
                name="hero-inbox"
                class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-2"
              />
            </div>
            <div
              :for={{id, task} <- @streams.todo}
              id={id}
              class="relative group card bg-base-100 border border-base-300 hover:border-primary hover:shadow-md transition-shadow transition-colors cursor-move"
              data-task-id={task.id}
              draggable="true"
              role="listitem"
            >
              <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <button
                  class="pointer-events-auto btn btn-ghost btn-xs"
                  phx-click="edit_task"
                  phx-value-task_id={task.id}
                  aria-label="Edit task"
                >
                  <.icon name="hero-pencil-square" class="w-4 h-4 text-base-content/70" />
                </button>
              </div>
              <div class="card-body p-2">
                <div class="flex justify-between items-start mb-1">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-mono text-gray-500 dark:text-gray-400">
                      {task.task_identifier}
                    </span>
                  </div>
                  <%= if task.due_date do %>
                    <span class={[
                      "text-xs",
                      if(Task.overdue?(task),
                        do: "text-red-600 dark:text-red-400 font-medium",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      {Calendar.strftime(task.due_date, "%m/%d")}
                    </span>
                  <% end %>
                </div>
                <h4 class="text-sm font-medium leading-tight mb-1 line-clamp-1">{task.title}</h4>
                <%= if task.assignee do %>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-1">
                      <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-white">
                            {String.first(task.assignee.email)}
                          </span>
                        </div>
                        <span class="text-xs opacity-80">{task.assignee.email}</span>
                      </div>
                    </div>
                    <%= if task.estimated_hours do %>
                      <span class="text-xs opacity-70">{task.estimated_hours}h</span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
<!-- In Progress Column -->
        <div
          class={[
            "bg-base-100 rounded-box shadow-sm min-h-96",
            status_color("in_progress")
          ]}
          data-status="in_progress"
          id="column-in_progress"
          data-dropzone="true"
          aria-label="In Progress column"
        >
          <div class="p-3 border-b border-base-300">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">In Progress</h3>
              <div class="flex items-center space-x-2">
                <span class="badge badge-ghost column-count" data-count>
                  {@counts["in_progress"] || 0}
                </span>
                <%= if @can_contribute do %>
                  <button
                    phx-click="show_task_form"
                    phx-value-status="in_progress"
                    class="btn btn-primary btn-circle btn-xs"
                  >
                    <.icon name="hero-plus" class="w-3 h-3" />
                  </button>
                <% end %>
              </div>
            </div>
          </div>
          <div class="p-2 space-y-2" id="tasks-in_progress" role="list" phx-update="stream">
            <div id="empty-in_progress" class="hidden only:block text-center py-6">
              <.icon
                name="hero-inbox"
                class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-2"
              />
            </div>
            <div
              :for={{id, task} <- @streams.in_progress}
              id={id}
              class="relative group card bg-base-100 border border-base-300 hover:border-primary hover:shadow-md transition-shadow transition-colors cursor-move"
              data-task-id={task.id}
              draggable="true"
              role="listitem"
            >
              <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <.icon name="hero-pencil-square" class="w-4 h-4 text-base-content/70" />
              </div>
              <div class="card-body p-2">
                <div class="flex justify-between items-start mb-1">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-mono text-gray-500 dark:text-gray-400">
                      {task.task_identifier}
                    </span>
                  </div>
                  <%= if task.due_date do %>
                    <span class={[
                      "text-xs",
                      if(Task.overdue?(task),
                        do: "text-red-600 dark:text-red-400 font-medium",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      {Calendar.strftime(task.due_date, "%m/%d")}
                    </span>
                  <% end %>
                </div>
                <h4 class="text-sm font-medium leading-tight mb-1 line-clamp-1">{task.title}</h4>
                <%= if task.assignee do %>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-1">
                      <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-white">
                            {String.first(task.assignee.email)}
                          </span>
                        </div>
                        <span class="text-xs opacity-80">{task.assignee.email}</span>
                      </div>
                    </div>
                    <%= if task.estimated_hours do %>
                      <span class="text-xs opacity-70">{task.estimated_hours}h</span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
<!-- Blocked Column -->
        <div
          class={[
            "bg-base-100 rounded-box shadow-sm min-h-96",
            status_color("blocked")
          ]}
          data-status="blocked"
          id="column-blocked"
          data-dropzone="true"
          aria-label="Blocked column"
        >
          <div class="p-3 border-b border-base-300">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">Blocked</h3>
              <div class="flex items-center space-x-2">
                <span class="badge badge-ghost column-count" data-count>
                  {@counts["blocked"] || 0}
                </span>
              </div>
            </div>
          </div>
          <div class="p-2 space-y-2" id="tasks-blocked" role="list" phx-update="stream">
            <div id="empty-blocked" class="hidden only:block text-center py-6">
              <.icon
                name="hero-inbox"
                class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-2"
              />
            </div>
            <div
              :for={{id, task} <- @streams.blocked}
              id={id}
              class="relative group card bg-base-100 border border-base-300 hover:border-primary hover:shadow-md transition-shadow transition-colors cursor-move"
              data-task-id={task.id}
              draggable="true"
              role="listitem"
            >
              <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <button
                  class="pointer-events-auto btn btn-ghost btn-xs"
                  phx-click="edit_task"
                  phx-value-task_id={task.id}
                  aria-label="Edit task"
                >
                  <.icon name="hero-pencil-square" class="w-4 h-4 text-base-content/70" />
                </button>
              </div>
              <div class="card-body p-2">
                <div class="flex justify-between items-start mb-1">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-mono text-gray-500 dark:text-gray-400">
                      {task.task_identifier}
                    </span>
                  </div>
                  <%= if task.due_date do %>
                    <span class={[
                      "text-xs",
                      if(Task.overdue?(task),
                        do: "text-red-600 dark:text-red-400 font-medium",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      {Calendar.strftime(task.due_date, "%m/%d")}
                    </span>
                  <% end %>
                </div>
                <h4 class="text-sm font-medium leading-tight mb-1 line-clamp-1">{task.title}</h4>
                <%= if task.assignee do %>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-1">
                      <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-white">
                            {String.first(task.assignee.email)}
                          </span>
                        </div>
                        <span class="text-xs opacity-80">{task.assignee.email}</span>
                      </div>
                    </div>
                    <%= if task.estimated_hours do %>
                      <span class="text-xs opacity-70">{task.estimated_hours}h</span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
        
<!-- Done Column -->
        <div
          class={[
            "bg-base-100 rounded-box shadow-sm min-h-96",
            status_color("done")
          ]}
          data-status="done"
          id="column-done"
          data-dropzone="true"
          aria-label="Done column"
        >
          <div class="p-3 border-b border-base-300">
            <div class="flex justify-between items-center">
              <h3 class="text-lg font-medium">Done</h3>
              <div class="flex items-center space-x-2">
                <span class="badge badge-ghost column-count" data-count>
                  {@counts["done"] || 0}
                </span>
              </div>
            </div>
          </div>
          <div class="p-2 space-y-2" id="tasks-done" role="list" phx-update="stream">
            <div id="empty-done" class="hidden only:block text-center py-6">
              <.icon
                name="hero-inbox"
                class="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-2"
              />
            </div>
            <div
              :for={{id, task} <- @streams.done}
              id={id}
              class="relative group card bg-base-100 border border-base-300 hover:border-primary hover:shadow-md transition-shadow transition-colors cursor-move"
              data-task-id={task.id}
              draggable="true"
              role="listitem"
            >
              <div class="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none">
                <button
                  class="pointer-events-auto btn btn-ghost btn-xs"
                  phx-click="edit_task"
                  phx-value-task_id={task.id}
                  aria-label="Edit task"
                >
                  <.icon name="hero-pencil-square" class="w-4 h-4 text-base-content/70" />
                </button>
              </div>
              <div class="card-body p-2">
                <div class="flex justify-between items-start mb-1">
                  <div class="flex items-center space-x-2">
                    <span class="text-xs font-mono text-gray-500 dark:text-gray-400">
                      {task.task_identifier}
                    </span>
                  </div>
                  <%= if task.due_date do %>
                    <span class={[
                      "text-xs",
                      if(Task.overdue?(task),
                        do: "text-red-600 dark:text-red-400 font-medium",
                        else: "text-gray-500 dark:text-gray-400"
                      )
                    ]}>
                      {Calendar.strftime(task.due_date, "%m/%d")}
                    </span>
                  <% end %>
                </div>
                <h4 class="text-sm font-medium leading-tight mb-1 line-clamp-1">{task.title}</h4>
                <%= if task.assignee do %>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-1">
                      <div class="flex items-center space-x-1">
                        <div class="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                          <span class="text-xs font-medium text-white">
                            {String.first(task.assignee.email)}
                          </span>
                        </div>
                        <span class="text-xs opacity-80">{task.assignee.email}</span>
                      </div>
                    </div>
                    <%= if task.estimated_hours do %>
                      <span class="text-xs opacity-70">{task.estimated_hours}h</span>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
<!-- Task Creation Modal -->
    <%= if @show_task_form do %>
      <div class="modal modal-open">
        <div
          class="modal-box w-11/12 max-w-md"
          phx-click-away="hide_task_form"
          phx-mounted={JS.focus(to: "##{@task_form[:title].id}")}
        >
          <h3 class="font-bold text-lg">
            Create Task in {status_display_name(@selected_status)}
          </h3>

          <.form for={@task_form} phx-submit="create_task" class="space-y-4">
            <div>
              <.input
                field={@task_form[:title]}
                type="text"
                label="Title"
                placeholder="Enter task title..."
                required
                autofocus
              />
            </div>

            <div>
              <.input
                field={@task_form[:description]}
                type="textarea"
                label="Description"
                placeholder="Describe the task..."
                rows="3"
              />
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <.input
                  field={@task_form[:priority]}
                  type="select"
                  label="Priority"
                  options={[
                    {"Low", "low"},
                    {"Medium", "medium"},
                    {"High", "high"},
                    {"Urgent", "urgent"}
                  ]}
                />
              </div>

              <div>
                <.input
                  field={@task_form[:estimated_hours]}
                  type="number"
                  label="Estimated Hours"
                  step="0.5"
                  min="0"
                />
              </div>
            </div>

            <div class="modal-action">
              <button type="button" phx-click="hide_task_form" class="btn btn-ghost">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary">Create Task</button>
            </div>
          </.form>
        </div>
        <div class="modal-backdrop" phx-click="hide_task_form"></div>
      </div>
    <% end %>
    
<!-- Task Edit Modal -->
    <%= if @show_edit_task_form && @selected_task do %>
      <div class="modal modal-open">
        <div
          class="modal-box w-11/12 max-w-md"
          phx-click-away="hide_edit_task_form"
          phx-mounted={JS.focus(to: "##{@edit_task_form[:title].id}")}
        >
          <h3 class="font-bold text-lg">
            Edit {@selected_task.task_identifier}
          </h3>

          <.form for={@edit_task_form} phx-submit="update_task" class="space-y-4">
            <div>
              <.input
                field={@edit_task_form[:title]}
                type="text"
                label="Title"
                placeholder="Enter task title..."
                required
                autofocus
              />
            </div>

            <div>
              <.input
                field={@edit_task_form[:description]}
                type="textarea"
                label="Description"
                placeholder="Describe the task..."
                rows="3"
              />
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <.input
                  field={@edit_task_form[:assignee_id]}
                  type="select"
                  label="Assignee"
                  options={@assignee_options}
                />
              </div>

              <div>
                <.input
                  field={@edit_task_form[:due_date]}
                  type="datetime-local"
                  label="Due Date"
                />
              </div>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <.input
                  field={@edit_task_form[:priority]}
                  type="select"
                  label="Priority"
                  options={[
                    {"Low", "low"},
                    {"Medium", "medium"},
                    {"High", "high"},
                    {"Urgent", "urgent"}
                  ]}
                />
              </div>

              <div>
                <.input
                  field={@edit_task_form[:estimated_hours]}
                  type="number"
                  label="Estimated Hours"
                  step="0.5"
                  min="0"
                />
              </div>
            </div>

            <div class="modal-action">
              <button type="button" phx-click="hide_edit_task_form" class="btn btn-ghost">
                Cancel
              </button>
              <button type="submit" class="btn btn-primary">Save Changes</button>
            </div>
          </.form>
        </div>
        <div class="modal-backdrop" phx-click="hide_edit_task_form"></div>
      </div>
    <% end %>
  </div>
</Layouts.app>
