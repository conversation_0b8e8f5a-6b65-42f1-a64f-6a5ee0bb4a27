defmodule CodebakeWeb.ProjectLive.Board do
  @moduledoc """
  LiveView for the Kanban board of a specific project.

  Displays tasks organized by status columns with drag-and-drop functionality,
  real-time updates, and presence indicators.
  """

  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization

  alias Codebake.Projects
  alias Codebake.Projects.Task

  @task_statuses ~w(todo in_progress blocked done)

  @impl true
  def mount(%{"project_id" => project_id}, _session, socket) do
    case load_and_authorize_project_lv(socket, project_id, :read_project) do
      {:ok, project, socket} ->
        if connected?(socket) do
          # Subscribe to project updates
          Phoenix.PubSub.subscribe(Codebake.PubSub, "project:#{project.id}")

          # Track presence for this board
          CodebakeWeb.Presence.track(
            self(),
            "project_board:#{project.id}",
            socket.assigns.current_scope.user.id,
            %{
              user: socket.assigns.current_scope.user,
              joined_at: System.system_time(:second)
            }
          )
        end

        members = Projects.list_project_members(project)
        assignee_options = [{"Unassigned", ""} | Enum.map(members, fn u -> {u.email, u.id} end)]

        socket =
          socket
          |> assign(:project, project)
          |> assign(:user_role, user_role_in_project(socket, project))
          |> assign(:can_manage, can_manage_project?(socket, project))
          |> assign(:can_contribute, can_contribute_to_project?(socket, project))
          |> assign(:show_task_form, false)
          |> assign(:task_form, to_form(%{}))
          |> assign(:selected_status, "todo")
          |> assign(:selected_task, nil)
          |> assign(:show_edit_task_form, false)
          |> assign(:edit_task_form, to_form(%{}))
          |> assign(:assignee_options, assignee_options)
          |> assign(:phases, Projects.list_phases(project))
          |> assign(:current_phase, Projects.current_phase(project))
          |> assign(:phase_form, to_form(%{"name" => ""}))
          |> refresh_board()

        {:ok, socket}

      {:error, socket} ->
        {:ok, socket}
    end
  end

  @impl true
  def handle_event("edit_task", params, socket) do
    task_id = Map.get(params, "task_id") || Map.get(params, "task-id")

    if is_nil(task_id) or task_id == "" do
      {:noreply, put_flash(socket, :error, "Task id missing")}
    else
      %{project: project, can_contribute: can_contribute} = socket.assigns

      if not can_contribute do
        {:noreply, put_flash(socket, :error, "You do not have permission to edit tasks")}
      else
        case Projects.get_task(project, task_id) do
          %Task{} = task ->
            changeset = Projects.change_task(task)

            socket =
              socket
              |> assign(:selected_task, task)
              |> assign(:show_edit_task_form, true)
              |> assign(:edit_task_form, to_form(changeset))

            {:noreply, socket}

          nil ->
            {:noreply, put_flash(socket, :error, "Task not found")}
        end
      end
    end
  end

  @impl true
  def handle_event("hide_edit_task_form", _params, socket) do
    {:noreply, assign(socket, :show_edit_task_form, false)}
  end

  @impl true
  def handle_event("show_task_form", %{"status" => status}, socket) do
    changeset = Task.create_changeset(%Task{}, %{})

    socket =
      socket
      |> assign(:show_task_form, true)
      |> assign(:selected_status, status)
      |> assign(:task_form, to_form(changeset))

    {:noreply, socket}
  end

  @impl true
  def handle_event("hide_task_form", _params, socket) do
    {:noreply, assign(socket, :show_task_form, false)}
  end

  @impl true
  def handle_event("update_task", %{"task" => task_params}, socket) do
    %{project: project, selected_task: task} = socket.assigns

    case Projects.update_task(task, task_params) do
      {:ok, updated} ->
        Phoenix.PubSub.broadcast(
          Codebake.PubSub,
          "project:#{project.id}",
          {:task_updated, updated}
        )

        socket =
          socket
          |> refresh_board()
          |> assign(:show_edit_task_form, false)
          |> assign(:selected_task, nil)
          |> put_flash(:info, "Task updated successfully!")

        {:noreply, socket}

      {:error, changeset} ->
        {:noreply,
         socket
         |> assign(:edit_task_form, to_form(changeset))
         |> put_flash(:error, "Failed to update task")}
    end
  end

  @impl true
  def handle_event("create_task", %{"task" => task_params}, socket) do
    %{
      project: project,
      current_scope: %{user: user},
      selected_status: status,
      current_phase: current_phase
    } = socket.assigns

    task_params =
      task_params
      |> Map.put("status", status)
      |> Map.put_new("phase_id", current_phase && current_phase["id"])

    case Projects.create_task(project, user, task_params) do
      {:ok, task} ->
        # Broadcast the new task to all connected clients
        Phoenix.PubSub.broadcast(Codebake.PubSub, "project:#{project.id}", {:task_created, task})

        # Reload tasks and update the board
        socket =
          socket
          |> refresh_board()
          |> assign(:show_task_form, false)
          |> put_flash(:info, "Task created successfully!")

        {:noreply, socket}

      {:error, changeset} ->
        socket =
          socket
          |> assign(:task_form, to_form(changeset))
          |> put_flash(:error, "Failed to create task")

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event(
        "move_task",
        %{"task_id" => task_id, "new_status" => new_status} = params,
        socket
      ) do
    %{project: project, can_contribute: can_contribute, current_scope: %{user: user}} =
      socket.assigns

    if not can_contribute do
      {:noreply, put_flash(socket, :error, "You do not have permission to move tasks")}
    else
      case Projects.get_task(project, task_id) do
        %Task{} = task ->
          before_id = Map.get(params, "before_id")

          opts =
            case new_status do
              "in_progress" -> [assign_if_unassigned_to: user.id]
              "todo" -> [unassign_if_moved_to_todo: true]
              _ -> []
            end

          case Projects.reorder_and_move_task(project, task, new_status, before_id, opts) do
            {:ok, updated_task} ->
              Phoenix.PubSub.broadcast(
                Codebake.PubSub,
                "project:#{project.id}",
                {:task_updated, updated_task}
              )

              socket =
                socket
                |> refresh_board()
                |> put_flash(:info, "Task moved to #{status_display_name(new_status)}")

              {:noreply, socket}

            {:error, _reason} ->
              {:noreply, put_flash(socket, :error, "Failed to move task")}
          end

        nil ->
          {:noreply, put_flash(socket, :error, "Task not found")}
      end
    end
  end

  @impl true
  def handle_event("show_task_details", %{"task_id" => task_id}, socket) do
    %{project: project} = socket.assigns

    case Projects.get_task(project, task_id) do
      %Task{} = task ->
        socket = assign(socket, :selected_task, task)
        {:noreply, socket}

      nil ->
        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("hide_task_details", _params, socket) do
    socket = assign(socket, :selected_task, nil)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:task_created, _task}, socket) do
    # Reload tasks when another user creates a task
    socket = refresh_board(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:task_updated, _task}, socket) do
    # Reload tasks when another user updates a task
    socket = refresh_board(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:project_updated, %Projects.Project{} = project}, socket) do
    # Update project and phase assigns when another client changes them
    socket =
      socket
      |> assign(:project, project)
      |> assign(:phases, Projects.list_phases(project))
      |> assign(:current_phase, Projects.current_phase(project))

    {:noreply, socket}
  end

  @impl true
  def handle_info(%{event: "presence_diff"}, socket) do
    # Handle presence updates (users joining/leaving the board)
    {:noreply, socket}
  end

  # Phase events
  @impl true
  def handle_event("set_current_phase", %{"phase_id" => phase_id}, socket) do
    %{project: project, can_manage: can_manage} = socket.assigns

    if not can_manage do
      {:noreply, put_flash(socket, :error, "You do not have permission to change phases")}
    else
      case Projects.set_current_phase(project, phase_id) do
        {:ok, proj} ->
          Phoenix.PubSub.broadcast(
            Codebake.PubSub,
            "project:#{project.id}",
            {:project_updated, proj}
          )

          socket =
            socket
            |> assign(:project, proj)
            |> assign(:current_phase, Projects.current_phase(proj))
            |> put_flash(:info, "Phase updated")

          {:noreply, socket}

        {:error, :not_found} ->
          {:noreply, put_flash(socket, :error, "Phase not found")}

        {:error, changeset} ->
          {:noreply, put_flash(socket, :error, "Failed to update phase")}
      end
    end
  end

  @impl true
  def handle_event("create_phase", %{"name" => name}, socket) do
    %{project: project, can_manage: can_manage} = socket.assigns

    if not can_manage do
      {:noreply, put_flash(socket, :error, "You do not have permission to create phases")}
    else
      case Projects.add_phase(project, name) do
        {:ok, proj} ->
          Phoenix.PubSub.broadcast(
            Codebake.PubSub,
            "project:#{project.id}",
            {:project_updated, proj}
          )

          socket =
            socket
            |> assign(:project, proj)
            |> assign(:phases, Projects.list_phases(proj))
            |> assign(:current_phase, Projects.current_phase(proj))
            |> assign(:phase_form, to_form(%{"name" => ""}))
            |> put_flash(:info, "Phase added")

          {:noreply, socket}

        {:error, _} ->
          {:noreply, put_flash(socket, :error, "Failed to add phase")}
      end
    end
  end

  # Private functions

  defp refresh_board(socket) do
    project = socket.assigns.project
    tasks = Projects.list_tasks(project, preload: [:assignee, :created_by])
    grouped = group_tasks_by_status(tasks)

    counts =
      Enum.reduce(@task_statuses, %{}, fn status, acc ->
        Map.put(acc, status, length(Map.get(grouped, status, [])))
      end)

    socket
    |> assign(:counts, counts)
    |> stream(:todo, Map.get(grouped, "todo", []), reset: true)
    |> stream(:in_progress, Map.get(grouped, "in_progress", []), reset: true)
    |> stream(:blocked, Map.get(grouped, "blocked", []), reset: true)
    |> stream(:done, Map.get(grouped, "done", []), reset: true)
  end

  defp group_tasks_by_status(tasks) do
    Enum.group_by(tasks, & &1.status, & &1)
    |> Map.new(fn {status, tasks} ->
      {status, Enum.sort_by(tasks, fn t -> {t.position || 0, t.task_number || 0} end)}
    end)
    |> ensure_all_statuses()
  end

  defp ensure_all_statuses(tasks_by_status) do
    Enum.reduce(@task_statuses, %{}, fn status, acc ->
      Map.put(acc, status, Map.get(tasks_by_status, status, []))
    end)
  end

  defp status_display_name("todo"), do: "To Do"
  defp status_display_name("in_progress"), do: "In Progress"
  defp status_display_name("blocked"), do: "Blocked"
  defp status_display_name("done"), do: "Done"

  defp status_color("todo"),
    do: "bg-gray-100 dark:bg-gray-700 border-gray-300 dark:border-gray-600"

  defp status_color("in_progress"),
    do: "bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-600"

  defp status_color("blocked"),
    do: "bg-red-100 dark:bg-red-900 border-red-300 dark:border-red-600"

  defp status_color("done"),
    do: "bg-green-100 dark:bg-green-900 border-green-300 dark:border-green-600"

  # DaisyUI badge variants for priority
  defp priority_color("low"), do: "badge-ghost"
  defp priority_color("medium"), do: "badge-warning"
  defp priority_color("high"), do: "badge-info"
  defp priority_color("urgent"), do: "badge-error"
end
