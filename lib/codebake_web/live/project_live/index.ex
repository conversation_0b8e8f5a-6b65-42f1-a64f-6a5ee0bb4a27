defmodule CodebakeWeb.ProjectLive.Index do
  @moduledoc """
  LiveView for listing and managing projects within an account.

  Shows all projects in an account and allows creating new projects.
  """

  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization

  alias Codebake.{Accounts, Projects}
  alias Codebake.Projects.Project

  @impl true
  def mount(%{"account_slug" => account_slug}, _session, socket) do
    case load_and_authorize_account_lv(socket, account_slug, :read_account) do
      {:ok, account, socket} ->
        user = current_user(socket)
        projects_with_roles = Projects.list_account_projects_with_user_roles(account, user)
        user_role = Accounts.get_user_account_role(user, account)

        socket =
          socket
          |> assign(:account, account)
          |> assign(:projects_with_roles, projects_with_roles)
          |> assign(:user_role, user_role)
          |> assign(:can_manage, user_role in ["admin", "owner"])
          # allow showing Create Project button
          |> assign(:can_contribute, user_role in ["admin", "owner"])
          |> assign(:show_create_form, false)
          |> assign(:form, to_form(%{}))

        {:ok, socket}

      {:error, socket} ->
        {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :index, _params) do
    socket
    |> assign(:page_title, "Projects")
    |> assign(:show_create_form, false)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Project")
    |> assign(:show_create_form, true)
    |> assign(:form, to_form(Project.create_changeset(%Project{}, %{})))
  end

  @impl true
  def handle_event("show_create_form", _params, socket) do
    changeset = Project.create_changeset(%Project{}, %{})

    socket =
      socket
      |> assign(:show_create_form, true)
      |> assign(:form, to_form(changeset))

    {:noreply, socket}
  end

  @impl true
  def handle_event("hide_create_form", _params, socket) do
    socket = assign(socket, :show_create_form, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("validate_project", %{"project" => project_params}, socket) do
    %{account: account, current_scope: %{user: user}} = socket.assigns

    changeset =
      %Project{}
      |> Project.create_changeset(
        project_params
        |> Map.put("account_id", account.id)
        |> Map.put("created_by_id", user.id)
      )
      |> Map.put(:action, :validate)

    socket = assign(socket, :form, to_form(changeset))
    {:noreply, socket}
  end

  @impl true
  def handle_event("create_project", %{"project" => project_params}, socket) do
    %{account: account, current_scope: %{user: user}} = socket.assigns

    case Projects.create_project(account, user, project_params) do
      {:ok, project} ->
        projects_with_roles = Projects.list_account_projects_with_user_roles(account, user)

        socket =
          socket
          |> assign(:projects_with_roles, projects_with_roles)
          |> assign(:show_create_form, false)
          |> push_navigate(to: ~p"/#{socket.assigns.account.slug}/projects/#{project.id}")

        {:noreply, socket}

      {:error, changeset} ->
        socket =
          socket
          |> assign(:form, to_form(changeset))

        {:noreply, socket}
    end
  end

  @impl true
  def handle_event("delete_project", %{"project_id" => project_id}, socket) do
    %{account: account, current_scope: %{user: user}} = socket.assigns

    case Projects.get_project(project_id) do
      %Project{} = project ->
        case Projects.delete_project(project) do
          {:ok, _project} ->
            projects_with_roles = Projects.list_account_projects_with_user_roles(account, user)

            socket =
              socket
              |> assign(:projects_with_roles, projects_with_roles)
              |> put_flash(:info, "Project deleted successfully!")

            {:noreply, socket}

          {:error, _changeset} ->
            socket = put_flash(socket, :error, "Failed to delete project")
            {:noreply, socket}
        end

      nil ->
        socket = put_flash(socket, :error, "Project not found")
        {:noreply, socket}
    end
  end

  # Helper functions

  # DaisyUI badge color variants for project statuses
  defp project_status_color("planning"), do: "badge-ghost"
  defp project_status_color("active"), do: "badge-success"
  defp project_status_color("on_hold"), do: "badge-warning"
  defp project_status_color("completed"), do: "badge-info"
  defp project_status_color("archived"), do: "badge-neutral"

  defp project_task_count(project) do
    # This would be more efficient with a counter field or preloaded association
    # For now, we'll show the task counter from the project
    project.task_counter || 0
  end
end
