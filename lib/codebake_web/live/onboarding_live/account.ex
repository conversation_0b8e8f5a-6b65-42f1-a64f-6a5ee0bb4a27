defmodule CodebakeWeb.OnboardingLive.Account do
  @moduledoc """
  Account onboarding flow for new users.

  This guides users through setting up their account name after first login,
  ensuring they have a proper company/organization account instead of the
  auto-generated personal account.
  """

  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization

  alias <PERSON>bake.Accounts
  alias Codebake.Accounts.Account

  @impl true
  def mount(_params, _session, socket) do
    if authenticated?(socket) do
      user = current_user(socket)

      # Check if user actually needs onboarding
      if Accounts.user_needs_account_setup?(user) do
        existing_account = Accounts.get_user_account(user)

        # Pre-fill with existing account data if available
        initial_attrs =
          if existing_account do
            %{
              "name" => existing_account.name,
              "billing_email" => existing_account.billing_email || user.email,
              "subscription_tier" => existing_account.subscription_tier || "freemium"
            }
          else
            %{
              "name" => generate_suggested_account_name(user),
              "billing_email" => user.email,
              "subscription_tier" => "freemium"
            }
          end

        changeset = Account.create_changeset(%Account{}, initial_attrs)

        socket =
          socket
          |> assign(:form, to_form(changeset))
          |> assign(:existing_account, existing_account)
          |> assign(:user, user)
          |> assign(:page_title, "Set Up Your Account")

        {:ok, socket}
      else
        # User doesn't need onboarding, redirect to their account dashboard
        case Accounts.get_user_account(user) do
          nil -> {:ok, redirect(socket, to: ~p"/onboarding/account")}
          account -> {:ok, redirect(socket, to: ~p"/#{account.slug}")}
        end
      end
    else
      {:ok, redirect(socket, to: ~p"/users/log-in")}
    end
  end

  @impl true
  def handle_event("validate", %{"account" => account_params}, socket) do
    changeset =
      %Account{}
      |> Account.create_changeset(account_params)
      |> Map.put(:action, :validate)

    socket = assign(socket, :form, to_form(changeset))
    {:noreply, socket}
  end

  @impl true
  def handle_event("setup_account", %{"account" => account_params}, socket) do
    user = socket.assigns.user
    existing_account = socket.assigns.existing_account

    if existing_account do
      # Update existing account
      case Accounts.update_account(existing_account, account_params) do
        {:ok, account} ->
          # Ensure creator is an Admin of the account
          _ = Accounts.ensure_account_membership(user, account, "owner")

          socket =
            socket
            |> put_flash(:info, "Account updated successfully! Welcome to #{account.name}!")
            |> redirect(to: ~p"/users/settings")

          {:noreply, socket}

        {:error, changeset} ->
          socket =
            socket
            |> assign(:form, to_form(changeset))
            |> put_flash(:error, "Failed to update account")

          {:noreply, socket}
      end
    else
      # Create new account
      case Accounts.create_account(account_params) do
        {:ok, account} ->
          # Update user to belong to this account
          case Accounts.update_user(user, %{account_id: account.id}) do
            {:ok, _user} ->
              # Ensure creator is an Admin of the account
              _ = Accounts.ensure_account_membership(user, account, "owner")

              socket =
                socket
                |> put_flash(:info, "Account created successfully! Welcome to #{account.name}!")
                |> redirect(to: ~p"/users/settings")

              {:noreply, socket}

            {:error, _changeset} ->
              # Clean up the account if user update fails
              Accounts.delete_account(account)

              socket =
                socket
                |> put_flash(:error, "Failed to associate user with account")

              {:noreply, socket}
          end

        {:error, changeset} ->
          socket =
            socket
            |> assign(:form, to_form(changeset))
            |> put_flash(:error, "Failed to create account")

          {:noreply, socket}
      end
    end
  end

  @impl true
  def handle_event("skip_for_now", _params, socket) do
    # Generate a random account name and create the account
    random_name = generate_random_account_name()

    account_params = %{
      "name" => random_name,
      "billing_email" => socket.assigns.current_scope.user.email,
      "subscription_tier" => "freemium"
    }

    case Accounts.create_account(account_params) do
      {:ok, account} ->
        # Associate the user with the account
        case Accounts.update_user(socket.assigns.current_scope.user, %{account_id: account.id}) do
          {:ok, _updated_user} ->
            # Ensure creator is an Admin of the account
            _ =
              Accounts.ensure_account_membership(
                socket.assigns.current_scope.user,
                account,
                "owner"
              )

            socket =
              socket
              |> put_flash(
                :info,
                "Account '#{random_name}' created! You can change the name anytime in settings."
              )
              |> redirect(to: ~p"/users/settings")

            {:noreply, socket}

          {:error, _changeset} ->
            # Clean up the account if user update fails
            Accounts.delete_account(account)

            socket =
              socket
              |> put_flash(:error, "Failed to create account")
              |> redirect(to: ~p"/onboarding/account")

            {:noreply, socket}
        end

      {:error, _changeset} ->
        socket =
          socket
          |> put_flash(:error, "Failed to create account")
          |> redirect(to: ~p"/onboarding/account")

        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <!-- Header -->
      <div class="sm:mx-auto sm:w-full sm:max-w-lg">
        <h1 class="text-center text-4xl font-bold text-gray-900 dark:text-white mb-3">
          Welcome to Codebake!
        </h1>
        <p class="text-center text-lg text-gray-600 dark:text-gray-400 mb-8">
          What's the name of your company or organization?
        </p>
      </div>

    <!-- Main Form -->
      <div class="sm:mx-auto sm:w-full sm:max-w-lg">
        <div class="bg-white dark:bg-gray-800 py-10 px-8 shadow-lg sm:rounded-xl border border-gray-200 dark:border-gray-700">
          <.form for={@form} phx-change="validate" phx-submit="setup_account" class="space-y-8">
            <!-- Primary Account Name Input - Made Prominent -->
            <div class="space-y-3">
              <label class="block text-lg font-semibold text-gray-900 dark:text-white">
                Account Name
              </label>
              <input
                type="text"
                name="account[name]"
                value={Phoenix.HTML.Form.input_value(@form, :name)}
                placeholder="e.g., Misfits, Acme Corp, University Research Lab"
                required
                class="block w-full px-4 py-4 text-xl border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-all duration-200"
              />
              <%= if @form.source.changes[:name] do %>
                <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mt-3">
                  <div class="flex items-center">
                    <.icon
                      name="hero-information-circle"
                      class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2 flex-shrink-0"
                    />
                    <div>
                      <p class="text-sm text-blue-800 dark:text-blue-200">
                        Your account URL will be:
                        <code class="font-mono bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded text-base font-semibold">
                          /{generate_slug_preview(@form.source.changes[:name])}
                        </code>
                      </p>
                    </div>
                  </div>
                </div>
              <% end %>
            </div>

    <!-- Action Buttons -->
            <div class="flex flex-col space-y-3 pt-4">
              <button
                type="submit"
                class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-4 px-6 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-lg"
                disabled={!@form.source.valid?}
              >
                {if @existing_account, do: "Update Account", else: "Create Account"}
              </button>

              <button
                type="button"
                phx-click="skip_for_now"
                class="w-full text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 font-medium py-2 transition-colors duration-200"
              >
                Skip for now
              </button>
            </div>
          </.form>

    <!-- Additional Settings - Moved to Bottom -->
          <details class="mt-8 group">
            <summary class="flex items-center justify-center cursor-pointer text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors">
              <span class="text-sm font-medium">Additional Settings</span>
              <.icon
                name="hero-chevron-down"
                class="w-4 h-4 ml-1 transition-transform group-open:rotate-180"
              />
            </summary>

            <div class="mt-6 space-y-6 border-t border-gray-200 dark:border-gray-600 pt-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Billing Email
                </label>
                <input
                  type="email"
                  name="account[billing_email]"
                  value={Phoenix.HTML.Form.input_value(@form, :billing_email)}
                  placeholder="<EMAIL>"
                  class="block w-full px-3 py-2 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-sm placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white transition-all duration-200"
                />
                <p class="mt-1 text-sm text-gray-500">
                  Email for billing notifications (defaults to your email)
                </p>
              </div>

              <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Plan
                </label>
                <select
                  name="account[subscription_tier]"
                  class="block w-full px-3 py-2 border-2 border-gray-300 dark:border-gray-600 rounded-lg shadow-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white bg-white transition-all duration-200"
                >
                  <option
                    value="freemium"
                    selected={Phoenix.HTML.Form.input_value(@form, :subscription_tier) == "freemium"}
                  >
                    Free (up to 3 members)
                  </option>
                  <option
                    value="team"
                    selected={Phoenix.HTML.Form.input_value(@form, :subscription_tier) == "team"}
                  >
                    Team (up to 10 members)
                  </option>
                  <option
                    value="business"
                    selected={Phoenix.HTML.Form.input_value(@form, :subscription_tier) == "business"}
                  >
                    Business (up to 50 members)
                  </option>
                  <option
                    value="enterprise"
                    selected={
                      Phoenix.HTML.Form.input_value(@form, :subscription_tier) == "enterprise"
                    }
                  >
                    Enterprise (unlimited)
                  </option>
                </select>
              </div>
            </div>
          </details>
        </div>
      </div>
    </div>
    """
  end

  # Helper to preview the slug that will be generated
  defp generate_slug_preview(name) when is_binary(name) do
    name
    |> String.downcase()
    |> String.replace(~r/[^a-z0-9\-]/, "-")
    |> String.replace(~r/-+/, "-")
    |> String.trim("-")
  end

  defp generate_slug_preview(_), do: "your-account"

  # Generate a suggested account name based on user's email domain
  defp generate_suggested_account_name(user) do
    case String.split(user.email, "@") do
      [_username, domain] ->
        domain
        |> String.split(".")
        |> List.first()
        |> String.replace(~r/[^a-zA-Z0-9]/, " ")
        |> String.split()
        |> Enum.map(&String.capitalize/1)
        |> Enum.join(" ")
        |> case do
          "" -> "My Company"
          name -> name
        end

      _ ->
        "My Company"
    end
  end

  # Generate a random account name using an adjective and themed noun (no number for presentation)
  defp generate_random_account_name do
    # Food and restaurant themed adjectives
    adjectives = [
      "Fresh",
      "Crispy",
      "Golden",
      "Savory",
      "Sweet",
      "Spicy",
      "Tender",
      "Juicy",
      "Creamy",
      "Smoky",
      "Zesty",
      "Rich",
      "Delicate",
      "Robust",
      "Hearty",
      "Flaky",
      "Buttery",
      "Silky",
      "Smooth",
      "Tangy",
      "Warm",
      "Cool",
      "Hot",
      "Mild",
      "Gourmet",
      "Artisan",
      "Rustic",
      "Classic",
      "Premium",
      "Signature",
      "Special",
      "Secret",
      "Homemade",
      "Organic",
      "Local",
      "Seasonal",
      "Fusion",
      "Traditional",
      "Modern",
      "Vintage"
    ]

    # Themed nouns around baking, chefs, and cooking to match CodeBake brand
    nouns = [
      "Kitchen",
      "Bakery",
      "Pantry",
      "Recipe",
      "Cookbook",
      "Oven",
      "Mixer",
      "Whisk",
      "Spatula",
      "Ladle",
      "Skillet",
      "Griddle",
      "Cauldron",
      "Kettle",
      "Pot",
      "Pan",
      "Feast",
      "Banquet",
      "Buffet",
      "Menu",
      "Dish",
      "Plate",
      "Bowl",
      "Spoon",
      "Chef",
      "Baker",
      "Cook",
      "Sous",
      "Pastry",
      "Bread",
      "Cake",
      "Pie",
      "Tart",
      "Muffin",
      "Scone",
      "Biscuit",
      "Croissant",
      "Bagel",
      "Donut",
      "Cookie",
      "Sauce",
      "Glaze",
      "Frosting",
      "Cream",
      "Butter",
      "Flour",
      "Sugar",
      "Spice"
    ]

    adjective = Enum.random(adjectives)
    noun = Enum.random(nouns)

    "#{adjective} #{noun}"
  end
end
