defmodule CodebakeWeb.UserLive.PersonalAccessTokens do
  use <PERSON><PERSON>ke<PERSON>eb, :live_view

  alias <PERSON><PERSON><PERSON>.Accounts
  alias <PERSON>bake.Accounts.PersonalAccessToken

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash} current_scope={@current_scope}>
      <div class="mx-auto max-w-4xl">
        <.header>
          Personal Access Tokens
          <:subtitle>
            Manage API tokens for external applications and AI agents.
          </:subtitle>
        </.header>

        <div class="mt-8 space-y-6">
          <!-- Create New Token Form -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h3 class="card-title">Create New Token</h3>

              <.form
                for={@form}
                id="token-form"
                phx-submit="create_token"
                phx-change="validate"
              >
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <.input
                    field={@form[:name]}
                    type="text"
                    label="Token Name"
                    placeholder="e.g., AI Assistant, CI/CD Pipeline"
                    required
                  />

                  <.input
                    field={@form[:expires_at]}
                    type="datetime-local"
                    label="Expires At (optional)"
                  />
                </div>

                <div class="mt-4">
                  <label class="label">
                    <span class="label-text font-semibold">Scopes</span>
                  </label>
                  <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                    <label
                      :for={scope <- PersonalAccessToken.available_scopes()}
                      class="label cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        name="token[scopes][]"
                        value={scope}
                        checked={scope in (@form[:scopes].value || [])}
                        class="checkbox checkbox-primary checkbox-sm"
                      />
                      <span class="label-text ml-2 text-sm">{scope}</span>
                    </label>
                  </div>
                </div>

                <div class="card-actions justify-end mt-6">
                  <.button type="submit" class="btn btn-primary">
                    Generate Token
                  </.button>
                </div>
              </.form>
            </div>
          </div>
          
    <!-- New Token Display -->
          <div :if={@new_token} class="alert alert-success">
            <.icon name="hero-check-circle" class="w-6 h-6" />
            <div class="flex-1">
              <h4 class="font-bold">Token Created Successfully!</h4>
              <p class="text-sm mt-1">
                Copy this token now - you won't be able to see it again.
              </p>
              <div class="mt-3 p-3 bg-base-200 rounded-lg font-mono text-sm break-all">
                {@new_token}
              </div>
            </div>
          </div>
          
    <!-- Existing Tokens -->
          <div class="card bg-base-100 shadow-xl">
            <div class="card-body">
              <h3 class="card-title">Your Tokens</h3>

              <div :if={@tokens == []} class="text-center py-8 text-base-content/60">
                <.icon name="hero-key" class="w-12 h-12 mx-auto mb-4" />
                <p>No tokens created yet.</p>
                <p class="text-sm">Create your first token to get started with the API.</p>
              </div>

              <div :if={@tokens != []} class="overflow-x-auto">
                <table class="table table-zebra">
                  <thead>
                    <tr>
                      <th>Name</th>
                      <th>Scopes</th>
                      <th>Last Used</th>
                      <th>Expires</th>
                      <th>Status</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr :for={token <- @tokens}>
                      <td>
                        <div class="font-semibold">{token.name}</div>
                        <div class="text-xs text-base-content/60">
                          Created {format_date(token.inserted_at)}
                        </div>
                      </td>
                      <td>
                        <div class="flex flex-wrap gap-1">
                          <span
                            :for={scope <- token.scopes}
                            class="badge badge-outline badge-xs"
                          >
                            {scope}
                          </span>
                        </div>
                      </td>
                      <td>
                        <span :if={token.last_used_at} class="text-sm">
                          {format_date(token.last_used_at)}
                        </span>
                        <span :if={!token.last_used_at} class="text-base-content/60 text-sm">
                          Never
                        </span>
                      </td>
                      <td>
                        <span :if={token.expires_at} class="text-sm">
                          {format_date(token.expires_at)}
                        </span>
                        <span :if={!token.expires_at} class="text-base-content/60 text-sm">
                          Never
                        </span>
                      </td>
                      <td>
                        <span class={[
                          "badge badge-xs",
                          token_status_class(token)
                        ]}>
                          {token_status(token)}
                        </span>
                      </td>
                      <td>
                        <div class="flex gap-2">
                          <button
                            :if={PersonalAccessToken.active?(token)}
                            phx-click="revoke_token"
                            phx-value-id={token.id}
                            data-confirm="Are you sure you want to revoke this token? This action cannot be undone."
                            class="btn btn-error btn-xs"
                          >
                            Revoke
                          </button>
                          <button
                            phx-click="view_logs"
                            phx-value-id={token.id}
                            class="btn btn-ghost btn-xs"
                          >
                            Logs
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layouts.app>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    user = socket.assigns.current_scope.user
    tokens = Accounts.list_personal_access_tokens(user)

    form = to_form(%{}, as: "token")

    {:ok, assign(socket, tokens: tokens, form: form, new_token: nil)}
  end

  @impl true
  def handle_event("validate", %{"token" => token_params}, socket) do
    form = to_form(token_params, as: "token")
    {:noreply, assign(socket, form: form)}
  end

  def handle_event("create_token", %{"token" => token_params}, socket) do
    user = socket.assigns.current_scope.user

    case Accounts.create_personal_access_token(user, token_params) do
      {:ok, token} ->
        tokens = Accounts.list_personal_access_tokens(user)
        form = to_form(%{}, as: "token")

        {:noreply,
         socket
         |> assign(tokens: tokens, form: form, new_token: token.token)
         |> put_flash(:info, "Personal access token created successfully!")}

      {:error, changeset} ->
        form = to_form(changeset, as: "token")
        {:noreply, assign(socket, form: form)}
    end
  end

  def handle_event("revoke_token", %{"id" => token_id}, socket) do
    user = socket.assigns.current_scope.user

    case Accounts.get_personal_access_token(user, token_id) do
      %PersonalAccessToken{} = token ->
        case Accounts.revoke_personal_access_token(token, user) do
          {:ok, _token} ->
            tokens = Accounts.list_personal_access_tokens(user)

            {:noreply,
             socket
             |> assign(tokens: tokens)
             |> put_flash(:info, "Token revoked successfully.")}

          {:error, _changeset} ->
            {:noreply, put_flash(socket, :error, "Failed to revoke token.")}
        end

      nil ->
        {:noreply, put_flash(socket, :error, "Token not found.")}
    end
  end

  def handle_event("view_logs", %{"id" => _token_id}, socket) do
    # TODO: Implement token logs view
    {:noreply, put_flash(socket, :info, "Token logs view coming soon!")}
  end

  defp format_date(datetime) do
    Calendar.strftime(datetime, "%b %d, %Y at %I:%M %p")
  end

  defp token_status(token) do
    cond do
      PersonalAccessToken.revoked?(token) -> "Revoked"
      PersonalAccessToken.expired?(token) -> "Expired"
      true -> "Active"
    end
  end

  defp token_status_class(token) do
    cond do
      PersonalAccessToken.revoked?(token) -> "badge-error"
      PersonalAccessToken.expired?(token) -> "badge-warning"
      true -> "badge-success"
    end
  end
end
