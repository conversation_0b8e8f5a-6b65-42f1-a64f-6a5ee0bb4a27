defmodule CodebakeWeb.UserLive.Login do
  use <PERSON>bake<PERSON>eb, :live_view

  alias <PERSON><PERSON><PERSON>.Accounts

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash} current_scope={@current_scope}>
      <div class="mx-auto max-w-sm space-y-4">
        <div class="text-center">
          <.header>
            <p>Log in</p>
            <:subtitle>
              <%= if @current_scope do %>
                You need to reauthenticate to perform sensitive actions on your account.
              <% else %>
                Don't have an account? <.link
                  navigate={~p"/users/register"}
                  class="font-semibold text-brand hover:underline"
                  phx-no-format
                >Sign up</.link> for an account now.
              <% end %>
            </:subtitle>
          </.header>
        </div>

        <div :if={@show_prompt} id="create-account-prompt" class="alert alert-warning">
          <.icon name="hero-question-mark-circle" class="size-6 shrink-0" />
          <div>
            <p>No account found for {@pending_email}.</p>
            <p class="mt-1">Would you like to create a new account with this email?</p>
            <div class="mt-3 flex gap-2">
              <button type="button" phx-click="create_account" class="btn btn-primary">
                Create account
              </button>
              <button type="button" phx-click="cancel_create_account" class="btn">
                Use a different email
              </button>
            </div>
          </div>
        </div>

        <div :if={local_mail_adapter?()} class="alert alert-info">
          <.icon name="hero-information-circle" class="size-6 shrink-0" />
          <div>
            <p>You are running the local mail adapter.</p>
            <p>
              To see sent emails, visit <.link href="/dev/mailbox" class="underline">the mailbox page</.link>.
            </p>
          </div>
        </div>

        <.form
          :let={f}
          for={@form}
          id="login_form_magic"
          action={~p"/users/log-in"}
          phx-submit="submit_magic"
        >
          <.input
            readonly={!!@current_scope}
            field={f[:email]}
            type="email"
            label="Email"
            autocomplete="username"
            required
            phx-mounted={JS.focus()}
          />
          <.button class="btn btn-primary w-full">
            Log in with email <span aria-hidden="true">→</span>
          </.button>
        </.form>
      </div>
    </Layouts.app>
    """
  end

  @impl true
  def mount(_params, _session, socket) do
    email =
      Phoenix.Flash.get(socket.assigns.flash, :email) ||
        get_in(socket.assigns, [:current_scope, Access.key(:user), Access.key(:email)])

    form = to_form(%{"email" => email}, as: "user")

    {:ok,
     assign(socket, form: form, trigger_submit: false, show_prompt: false, pending_email: nil)}
  end

  @impl true
  def handle_event("submit_password", _params, socket) do
    {:noreply, assign(socket, :trigger_submit, true)}
  end

  def handle_event("submit_magic", %{"user" => %{"email" => email}}, socket) do
    case Accounts.get_user_by_email(email) do
      %_{} = user ->
        Accounts.deliver_login_instructions(user, &url(~p"/users/log-in/#{&1}"))

        info =
          "If your email is in our system, you will receive instructions for logging in shortly."

        {:noreply,
         socket
         |> put_flash(:info, info)
         |> push_navigate(to: ~p"/users/log-in")}

      nil ->
        {:noreply, assign(socket, show_prompt: true, pending_email: email)}
    end
  end

  def handle_event("create_account", _params, %{assigns: %{pending_email: email}} = socket) do
    # Create the user immediately and send the magic link
    case Accounts.register_user(%{"email" => email}) do
      {:ok, user} ->
        Accounts.deliver_login_instructions(user, &url(~p"/users/log-in/#{&1}"))

        info =
          "If your email is in our system, you will receive instructions for logging in shortly."

        {:noreply,
         socket
         |> put_flash(:info, info)
         |> push_navigate(to: ~p"/users/log-in")}

      {:error, _changeset} ->
        # If there was a race and the user now exists, just send the login email.
        case Accounts.get_user_by_email(email) do
          %_{} = user ->
            Accounts.deliver_login_instructions(user, &url(~p"/users/log-in/#{&1}"))

            {:noreply,
             socket
             |> put_flash(
               :info,
               "If your email is in our system, you will receive instructions for logging in shortly."
             )
             |> push_navigate(to: ~p"/users/log-in")}

          nil ->
            {:noreply,
             socket
             |> put_flash(:error, "Could not create account at this time. Please try again.")}
        end
    end
  end

  def handle_event("cancel_create_account", _params, socket) do
    {:noreply, assign(socket, show_prompt: false)}
  end

  defp local_mail_adapter? do
    Application.get_env(:codebake, Codebake.Mailer)[:adapter] == Swoosh.Adapters.Local
  end
end
