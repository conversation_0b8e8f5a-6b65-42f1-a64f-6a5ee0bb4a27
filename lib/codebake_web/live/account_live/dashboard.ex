defmodule CodebakeWeb.AccountLive.Dashboard do
  @moduledoc """
  Account-level dashboard showing all projects and their details.

  This is the main dashboard for account members to see an overview
  of all projects within their organization and their access levels.
  """

  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization

  alias Codebake.{Accounts, Projects}

  @impl true
  def mount(%{"account_slug" => account_slug}, _session, socket) do
    case load_and_authorize_account_lv(socket, account_slug, :read_account) do
      {:ok, account, socket} ->
        user = current_user(socket)
        projects_with_roles = Projects.list_account_projects_with_user_roles(account, user)
        stats = get_account_project_stats(account, user)
        user_role = Accounts.get_user_account_role(user, account)

        socket =
          socket
          |> assign(:account, account)
          |> assign(:projects_with_roles, projects_with_roles)
          |> assign(:stats, stats)
          |> assign(:user_role, user_role)
          |> assign(:can_manage, user_role in ["admin", "owner"])

        {:ok, socket}

      {:error, socket} ->
        {:ok, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash} current_scope={@current_scope}>
      <div class="max-w-7xl mx-auto p-6">
        
    <!-- Projects Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg border border-gray-200 dark:border-gray-700">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <div class="flex justify-between items-center">
              <h2 class="text-xl font-semibold text-gray-900 dark:text-white">Projects</h2>
              <%= if @can_manage do %>
                <.link navigate={~p"/#{@account.slug}/projects/new"} class="btn btn-primary btn-sm">
                  Create Project
                </.link>
              <% end %>
            </div>
          </div>

          <div class="p-6">
            <%= if Enum.empty?(@projects_with_roles) do %>
              <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                  <.icon name="hero-folder" class="w-16 h-16 mx-auto" />
                </div>
                <h3 class="text-lg font-medium mb-2">No projects yet</h3>
                <p class="text-gray-500 mb-4">
                  Create your first project to get started with task management.
                </p>
                <%= if @can_manage do %>
                  <.link navigate={~p"/#{@account.slug}/projects/new"} class="btn btn-primary">
                    Create Your First Project
                  </.link>
                <% end %>
              </div>
            <% else %>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <%= for %{project: project, user_role: user_role, is_member: is_member} <- @projects_with_roles do %>
                  <div class="card bg-base-100 border border-base-300 hover:shadow-md transition-shadow">
                    <div class="card-body">
                      <div class="flex justify-between items-start mb-2">
                        <h3 class="card-title text-lg font-semibold">{project.name}</h3>
                        <div class="flex gap-2">
                          <div class={[
                            "badge",
                            case project.status do
                              "active" -> "badge-success"
                              "planning" -> "badge-warning"
                              "on_hold" -> "badge-error"
                              "completed" -> "badge-info"
                              _ -> "badge-ghost"
                            end
                          ]}>
                            {String.capitalize(project.status)}
                          </div>
                          <%= if is_member do %>
                            <div class="badge badge-outline">
                              {String.capitalize(user_role || "viewer")}
                            </div>
                          <% end %>
                        </div>
                      </div>

                      <%= if project.description do %>
                        <p class="text-sm mb-4 opacity-80">{project.description}</p>
                      <% end %>

                      <div class="flex justify-between items-center">
                        <%= if is_member do %>
                          <.link
                            navigate={~p"/#{@account.slug}/projects/#{project.id}"}
                            class="btn btn-primary btn-sm"
                          >
                            Open Project
                          </.link>
                          <.link
                            navigate={~p"/#{@account.slug}/projects/#{project.id}/board"}
                            class="btn btn-outline btn-sm"
                          >
                            Board
                          </.link>
                        <% else %>
                          <span class="text-sm text-gray-500">No access</span>
                          <%= if @can_manage do %>
                            <button class="btn btn-outline btn-sm" disabled>
                              Request Access
                            </button>
                          <% end %>
                        <% end %>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            <% end %>
          </div>
        </div>
      </div>
    </Layouts.app>
    """
  end

  # Helper function to get account project statistics
  defp get_account_project_stats(account, user) do
    all_projects = Projects.list_projects(account)
    user_projects = Projects.list_user_projects(user, account_id: account.id)

    %{
      total_project_count: length(all_projects),
      user_project_count: length(user_projects),
      active_project_count: Enum.count(all_projects, &(&1.status == "active")),
      completed_project_count: Enum.count(all_projects, &(&1.status == "completed"))
    }
  end
end
