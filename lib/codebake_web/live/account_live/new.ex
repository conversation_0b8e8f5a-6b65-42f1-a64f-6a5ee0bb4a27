defmodule CodebakeWeb.AccountLive.New do
  @moduledoc """
  LiveView for creating a new account (company/organization).

  This allows users to create a proper company account instead of
  the auto-generated personal accounts.
  """

  use CodebakeWeb, :live_view
  import CodebakeWeb.Authorization

  alias Codebake.Accounts
  alias Codebake.Accounts.Account

  @impl true
  def mount(_params, _session, socket) do
    if authenticated?(socket) do
      changeset = Account.create_changeset(%Account{}, %{})

      socket =
        socket
        |> assign(:form, to_form(changeset))
        |> assign(:page_title, "Create Account")

      {:ok, socket}
    else
      {:ok, redirect(socket, to: ~p"/users/log-in")}
    end
  end

  @impl true
  def handle_event("validate", %{"account" => account_params}, socket) do
    changeset =
      %Account{}
      |> Account.create_changeset(account_params)
      |> Map.put(:action, :validate)

    socket = assign(socket, :form, to_form(changeset))
    {:noreply, socket}
  end

  @impl true
  def handle_event("create_account", %{"account" => account_params}, socket) do
    user = current_user(socket)

    case Accounts.create_account(account_params) do
      {:ok, account} ->
        # Update user to belong to this account
        case Accounts.update_user(user, %{account_id: account.id}) do
          {:ok, _user} ->
            socket =
              socket
              # Updated copy: teams -> projects; team concept was removed in favor of projects
              |> put_flash(:info, "Account created successfully! You can now create projects.")
              |> redirect(to: ~p"/#{account.slug}")

            {:noreply, socket}

          {:error, _changeset} ->
            # Clean up the account if user update fails
            Accounts.delete_account(account)

            socket =
              socket
              |> put_flash(:error, "Failed to associate user with account")

            {:noreply, socket}
        end

      {:error, changeset} ->
        socket =
          socket
          |> assign(:form, to_form(changeset))
          |> put_flash(:error, "Failed to create account")

        {:noreply, socket}
    end
  end

  @impl true
  def render(assigns) do
    ~H"""
    <Layouts.app flash={@flash} current_scope={@current_scope}>
      <div class="max-w-2xl mx-auto p-6">
        <div class="mb-6">
          <.link navigate={~p"/"} class="text-blue-600 hover:text-blue-800">
            ← Back to Dashboard
          </.link>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 border border-gray-200 dark:border-gray-700">
          <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Create Account</h1>

          <p class="text-gray-600 dark:text-gray-400 mb-6">
            <%!-- Updated copy: removed 'teams' reference after team tables were dropped; use 'organization and projects' --%>
            Create an account for your company or organization. This will serve as the billing
            and administrative boundary for your organization and projects.
          </p>

          <.form for={@form} phx-change="validate" phx-submit="create_account" class="space-y-6">
            <div>
              <.input
                field={@form[:name]}
                type="text"
                label="Account Name"
                placeholder="e.g., Misfits, Acme Corp, University Research Lab"
                required
              />
              <p class="mt-1 text-sm text-gray-500">
                This will be the display name for your organization.
              </p>
            </div>

            <div>
              <.input
                field={@form[:billing_email]}
                type="email"
                label="Billing Email"
                placeholder="<EMAIL>"
              />
              <p class="mt-1 text-sm text-gray-500">
                Email address for billing and administrative notifications.
              </p>
            </div>

            <div>
              <.input
                field={@form[:subscription_tier]}
                type="select"
                label="Plan"
                options={[
                  {"Free (up to 3 members)", "freemium"},
                  {"Team (up to 10 members)", "team"},
                  {"Business (up to 50 members)", "business"},
                  {"Enterprise (unlimited)", "enterprise"}
                ]}
              />
            </div>

            <%= if @form.source.changes[:name] do %>
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <div class="flex items-center">
                  <.icon
                    name="hero-information-circle"
                    class="w-5 h-5 text-blue-600 dark:text-blue-400 mr-2"
                  />
                  <div>
                    <p class="text-sm text-blue-800 dark:text-blue-200">
                      Your account URL will be:
                      <code class="font-mono bg-blue-100 dark:bg-blue-800 px-1 rounded">
                        /{generate_slug_preview(@form.source.changes[:name])}
                      </code>
                    </p>
                  </div>
                </div>
              </div>
            <% end %>

            <div class="flex justify-end space-x-3">
              <.link navigate={~p"/"} class="btn btn-ghost">
                Cancel
              </.link>
              <button type="submit" class="btn btn-primary" disabled={!@form.source.valid?}>
                Create Account
              </button>
            </div>
          </.form>
        </div>
      </div>
    </Layouts.app>
    """
  end

  # Helper to preview the slug that will be generated
  defp generate_slug_preview(name) when is_binary(name) do
    name
    |> String.downcase()
    |> String.replace(~r/[^a-z0-9\-]/, "-")
    |> String.replace(~r/-+/, "-")
    |> String.trim("-")
  end

  defp generate_slug_preview(_), do: "your-account"
end
