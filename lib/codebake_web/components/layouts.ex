defmodule CodebakeWeb.Layouts do
  @moduledoc """
  This module holds layouts and related functionality
  used by your application.
  """
  use CodebakeWeb, :html

  # Embed all files in layouts/* within this module.
  # The default root.html.heex file contains the HTML
  # skeleton of your application, namely HTML headers
  # and other static content.
  embed_templates "layouts/*"

  @doc """
  Renders your app layout.

  This function is typically invoked from every template,
  and it often contains your application menu, sidebar,
  or similar.

  ## Examples

      <Layouts.app flash={@flash}>
        <h1>Content</h1>
      </Layouts.app>

  """
  attr :flash, :map, required: true, doc: "the map of flash messages"

  attr :current_scope, :map,
    default: nil,
    doc: "the current [scope](https://hexdocs.pm/phoenix/scopes.html)"

  slot :inner_block, required: true

  def app(assigns) do
    ~H"""
    <div class="min-h-screen bg-base-100">
      <!-- Navigation Header -->
      <header class="navbar bg-base-100 border-b border-base-300 px-4 sm:px-6 lg:px-8">
        <div class="navbar-start">
          <.link navigate="/" class="btn btn-ghost text-xl font-bold">
            <.icon name="hero-cube" class="w-6 h-6 text-primary" />
            <%= if @current_scope && @current_scope.account && @current_scope.account.name do %>
              {@current_scope.account.name}
            <% else %>
              Codebake
            <% end %>
          </.link>
        </div>

        <div class="navbar-center hidden lg:flex">
          <ul :if={@current_scope} class="menu menu-horizontal px-1">
            <li><.link navigate="/projects" class="btn btn-ghost">Projects</.link></li>
            <li><.link navigate="/tasks" class="btn btn-ghost">My Tasks</.link></li>
          </ul>
        </div>

        <div class="navbar-end">
          <div class="flex items-center gap-2">
            <.theme_toggle />

            <div :if={@current_scope} class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                <div class="w-8 rounded-full">
                  <img
                    :if={@current_scope.user.avatar_url}
                    src={@current_scope.user.avatar_url}
                    alt={@current_scope.user.name || @current_scope.user.email}
                  />
                  <div
                    :if={!@current_scope.user.avatar_url}
                    class="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center text-sm font-semibold"
                  >
                    {user_initials(@current_scope.user)}
                  </div>
                </div>
              </div>
              <ul
                tabindex="0"
                class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"
              >
                <li class="menu-title">
                  <span>{@current_scope.user.name || @current_scope.user.email}</span>
                </li>
                <li><.link navigate="/users/settings">Settings</.link></li>
                <li><.link navigate="/users/settings/tokens">API Tokens</.link></li>
                <li class="divider"></li>
                <li><.link href="/users/log-out" method="delete">Logout</.link></li>
              </ul>
            </div>

            <div :if={!@current_scope} class="flex gap-2">
              <.link navigate="/users/log-in" class="btn btn-ghost">Sign In</.link>
              <.link navigate="/users/register" class="btn btn-primary">Sign Up</.link>
            </div>
          </div>
        </div>
      </header>
      
    <!-- Main Content -->
      <main class="flex-1">
        <div class="container mx-auto px-4 py-6 sm:px-6 lg:px-8">
          {render_slot(@inner_block)}
        </div>
      </main>
      
    <!-- Flash Messages -->
      <.flash_group flash={@flash} />
    </div>
    """
  end

  @doc """
  Shows the flash group with standard titles and content.

  ## Examples

      <.flash_group flash={@flash} />
  """
  attr :flash, :map, required: true, doc: "the map of flash messages"
  attr :id, :string, default: "flash-group", doc: "the optional id of flash container"

  def flash_group(assigns) do
    ~H"""
    <div id={@id} aria-live="polite" class="fixed top-4 right-4 z-50 space-y-2">
      <.flash kind={:error} flash={@flash} />

      <.flash
        id="client-error"
        kind={:error}
        title={gettext("We can't find the internet")}
        phx-disconnected={show(".phx-client-error #client-error") |> JS.remove_attribute("hidden")}
        phx-connected={hide("#client-error") |> JS.set_attribute({"hidden", ""})}
        hidden
      >
        {gettext("Attempting to reconnect")}
        <.icon name="hero-arrow-path" class="ml-1 size-3 motion-safe:animate-spin" />
      </.flash>

      <.flash
        id="server-error"
        kind={:error}
        title={gettext("Something went wrong!")}
        phx-disconnected={show(".phx-server-error #server-error") |> JS.remove_attribute("hidden")}
        phx-connected={hide("#server-error") |> JS.set_attribute({"hidden", ""})}
        hidden
      >
        {gettext("Attempting to reconnect")}
        <.icon name="hero-arrow-path" class="ml-1 size-3 motion-safe:animate-spin" />
      </.flash>
    </div>
    """
  end

  @doc """
  Provides dark vs light theme toggle based on themes defined in app.css.

  See <head> in root.html.heex which applies the theme before page load.
  """
  def theme_toggle(assigns) do
    ~H"""
    <div class="card relative flex flex-row items-center border-2 border-base-300 bg-base-300 rounded-full">
      <div class="absolute w-1/3 h-full rounded-full border-1 border-base-200 bg-base-100 brightness-200 left-0 [[data-theme=light]_&]:left-1/3 [[data-theme=dark]_&]:left-2/3 transition-[left]" />

      <button
        class="flex p-2 cursor-pointer w-1/3"
        phx-click={JS.dispatch("phx:set-theme", detail: %{theme: "system"})}
        data-phx-theme="system"
      >
        <.icon name="hero-computer-desktop-micro" class="size-4 opacity-75 hover:opacity-100" />
      </button>

      <button
        class="flex p-2 cursor-pointer w-1/3"
        phx-click={JS.dispatch("phx:set-theme", detail: %{theme: "light"})}
        data-phx-theme="light"
      >
        <.icon name="hero-sun-micro" class="size-4 opacity-75 hover:opacity-100" />
      </button>

      <button
        class="flex p-2 cursor-pointer w-1/3"
        phx-click={JS.dispatch("phx:set-theme", detail: %{theme: "dark"})}
        data-phx-theme="dark"
      >
        <.icon name="hero-moon-micro" class="size-4 opacity-75 hover:opacity-100" />
      </button>
    </div>
    """
  end

  @doc """
  Renders a sidebar layout for authenticated pages.

  ## Examples

      <Layouts.sidebar flash={@flash} current_scope={@current_scope}>
        <:sidebar>
          <nav>...</nav>
        </:sidebar>
        <h1>Main content</h1>
      </Layouts.sidebar>
  """
  attr :flash, :map, required: true
  attr :current_scope, :map, required: true
  slot :sidebar, required: true
  slot :inner_block, required: true

  def sidebar(assigns) do
    ~H"""
    <div class="min-h-screen bg-base-100">
      <!-- Top Navigation -->
      <header class="navbar bg-base-100 border-b border-base-300 px-4 sm:px-6 lg:px-8">
        <div class="navbar-start">
          <label for="sidebar-drawer" class="btn btn-square btn-ghost lg:hidden">
            <.icon name="hero-bars-3" class="w-6 h-6" />
          </label>
          <.link navigate="/" class="btn btn-ghost text-xl font-bold">
            <.icon name="hero-cube" class="w-6 h-6 text-primary" />
            <%= if @current_scope && @current_scope.account && @current_scope.account.name do %>
              {@current_scope.account.name}
            <% else %>
              Codebake
            <% end %>
          </.link>
        </div>

        <div class="navbar-end">
          <div class="flex items-center gap-2">
            <.theme_toggle />

            <div class="dropdown dropdown-end">
              <div tabindex="0" role="button" class="btn btn-ghost btn-circle avatar">
                <div class="w-8 rounded-full">
                  <img
                    :if={@current_scope.user.avatar_url}
                    src={@current_scope.user.avatar_url}
                    alt={@current_scope.user.name || @current_scope.user.email}
                  />
                  <div
                    :if={!@current_scope.user.avatar_url}
                    class="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center text-sm font-semibold"
                  >
                    {user_initials(@current_scope.user)}
                  </div>
                </div>
              </div>
              <ul
                tabindex="0"
                class="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52"
              >
                <li class="menu-title">
                  <span>{@current_scope.user.name || @current_scope.user.email}</span>
                </li>
                <li><.link navigate="/users/settings">Settings</.link></li>
                <li><.link navigate="/users/settings/tokens">API Tokens</.link></li>
                <li class="divider"></li>
                <li><.link href="/users/log-out" method="delete">Logout</.link></li>
              </ul>
            </div>
          </div>
        </div>
      </header>
      
    <!-- Drawer Layout -->
      <div class="drawer lg:drawer-open">
        <input id="sidebar-drawer" type="checkbox" class="drawer-toggle" />
        
    <!-- Main Content -->
        <div class="drawer-content flex flex-col">
          <main class="flex-1 p-6">
            {render_slot(@inner_block)}
          </main>
        </div>
        
    <!-- Sidebar -->
        <div class="drawer-side">
          <label for="sidebar-drawer" aria-label="close sidebar" class="drawer-overlay"></label>
          <aside class="min-h-full w-64 bg-base-200 text-base-content">
            {render_slot(@sidebar)}
          </aside>
        </div>
      </div>
      
    <!-- Flash Messages -->
      <.flash_group flash={@flash} />
    </div>
    """
  end

  # Helper function to generate user initials for avatar
  defp user_initials(user) do
    name = user.name || user.email

    name
    |> String.split()
    |> Enum.take(2)
    |> Enum.map(&String.first/1)
    |> Enum.join()
    |> String.upcase()
  end
end
