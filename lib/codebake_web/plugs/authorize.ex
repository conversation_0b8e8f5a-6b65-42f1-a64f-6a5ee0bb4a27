defmodule CodebakeWeb.Plugs.Authorize do
  @moduledoc """
  Plug for authorization in the web layer.

  This plug provides authorization checks that can be used in router pipelines
  or individual controller actions.

  ## Usage in Router

      scope "/projects/:project_id", CodebakeWeb do
        pipe_through [:browser, :require_authenticated_user, :authorize_project_access]

        live "/", ProjectLive.Board, :show
        live "/settings", ProjectLive.Settings, :edit
      end

      # Custom pipeline for project authorization
      pipeline :authorize_project_access do
        plug CodebakeWeb.Plugs.Authorize, action: :read_project, resource: :project
      end

  ## Usage in Controllers

      defmodule MyController do
        use CodebakeWeb, :controller

        plug CodebakeWeb.Plugs.Authorize, action: :manage_team, resource: :team when action in [:edit, :update, :delete]

        def show(conn, _params) do
          # Team is already loaded and authorized
          team = conn.assigns.team
          render(conn, :show, team: team)
        end
      end
  """

  import Plug.Conn
  import Phoenix.Controller, only: [json: 2, redirect: 2]

  alias Codebake.{Authorization, Projects}

  @doc """
  Initialize the plug with options.

  ## Options

    * `:action` - The action to authorize (required)
    * `:resource` - How to get the resource to authorize against
      - `:project` - Load project from `:project_id` param
      - `{:project, :custom_param}` - Load project from custom param
      - `{:assign, :project}` - Use project from conn assigns
      - `{:function, {Module, :function_name}}` - Call custom function
    * `:on_unauthorized` - What to do when unauthorized
      - `:json` (default) - Return JSON error
      - `:redirect` - Redirect to a page
      - `{:redirect, path}` - Redirect to specific path
      - `{:function, {Module, :function_name}}` - Call custom function

  ## Examples

      # Authorize project read access, load project from :project_id param
      plug CodebakeWeb.Plugs.Authorize, action: :read_project, resource: :project

      # Authorize project management, project already in assigns
      plug CodebakeWeb.Plugs.Authorize, action: :manage_project, resource: {:assign, :project}

      # Custom unauthorized handling
      plug CodebakeWeb.Plugs.Authorize,
        action: :read_project,
        resource: :project,
        on_unauthorized: {:redirect, "/unauthorized"}
  """
  def init(opts) do
    action = Keyword.fetch!(opts, :action)
    resource = Keyword.get(opts, :resource, :project)
    on_unauthorized = Keyword.get(opts, :on_unauthorized, :json)

    %{
      action: action,
      resource: resource,
      on_unauthorized: on_unauthorized
    }
  end

  @doc """
  Perform the authorization check.
  """
  def call(conn, %{action: action, resource: resource_spec, on_unauthorized: on_unauthorized}) do
    with {:ok, resource} <- load_resource(conn, resource_spec),
         :ok <- authorize_action(conn, action, resource) do
      # Store the resource in assigns for use in controllers
      assign_resource(conn, resource_spec, resource)
    else
      {:error, :not_found} ->
        handle_not_found(conn)

      {:error, :unauthorized} ->
        handle_unauthorized(conn, on_unauthorized)

      {:error, reason} ->
        handle_error(conn, reason)
    end
  end

  ## Resource loading

  defp load_resource(conn, :project) do
    load_resource(conn, {:project, :project_id})
  end

  defp load_resource(conn, {:project, param_key}) do
    case Map.get(conn.params, to_string(param_key)) do
      nil ->
        {:error, :missing_param}

      project_id ->
        case Projects.get_project(project_id) do
          nil -> {:error, :not_found}
          project -> {:ok, project}
        end
    end
  end

  defp load_resource(conn, {:assign, assign_key}) do
    case Map.get(conn.assigns, assign_key) do
      nil -> {:error, :not_found}
      resource -> {:ok, resource}
    end
  end

  defp load_resource(conn, {:function, {module, function_name}}) do
    apply(module, function_name, [conn])
  end

  defp load_resource(_conn, resource) when not is_atom(resource) do
    # Resource is already loaded
    {:ok, resource}
  end

  ## Authorization

  defp authorize_action(conn, action, resource) do
    Bodyguard.permit(Authorization, action, conn.assigns.current_scope, resource)
  end

  ## Resource assignment

  defp assign_resource(conn, :team, resource), do: assign(conn, :team, resource)
  defp assign_resource(conn, {:team, _}, resource), do: assign(conn, :team, resource)
  defp assign_resource(conn, {:assign, key}, resource), do: assign(conn, key, resource)
  defp assign_resource(conn, _, _resource), do: conn

  ## Error handling

  defp handle_not_found(conn) do
    conn
    |> put_status(:not_found)
    |> json(%{error: "Resource not found"})
    |> halt()
  end

  defp handle_unauthorized(conn, :json) do
    conn
    |> put_status(:forbidden)
    |> json(%{error: "You don't have permission to perform this action"})
    |> halt()
  end

  defp handle_unauthorized(conn, :redirect) do
    conn
    |> redirect(to: "/")
    |> halt()
  end

  defp handle_unauthorized(conn, {:redirect, path}) do
    conn
    |> redirect(to: path)
    |> halt()
  end

  defp handle_unauthorized(conn, {:function, {module, function_name}}) do
    apply(module, function_name, [conn])
    |> halt()
  end

  defp handle_error(conn, reason) do
    conn
    |> put_status(:internal_server_error)
    |> json(%{error: "An error occurred: #{inspect(reason)}"})
    |> halt()
  end
end
