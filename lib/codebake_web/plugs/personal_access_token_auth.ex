defmodule CodebakeWeb.Plugs.PersonalAccessTokenAuth do
  @moduledoc """
  Plug for authenticating requests using Personal Access Tokens.

  This plug is used for API endpoints that need to authenticate AI agents
  and other external clients using PATs.
  """

  import Plug.Conn
  import Phoenix.Controller, only: [json: 2]

  alias <PERSON>bake.Accounts
  alias Codebake.Accounts.{PersonalAccessToken, PersonalAccessTokenLog}

  @behaviour Plug

  def init(opts), do: opts

  def call(conn, opts) do
    with {:ok, token_string} <- extract_token(conn),
         {:ok, token} <- authenticate_token(token_string, get_client_ip(conn)),
         :ok <- check_scopes(token, opts) do
      # Log the authentication
      log_authentication(token, conn)

      # Assign token and user to conn
      conn
      |> assign(:current_pat, token)
      |> assign(:current_scope, Codebake.Accounts.Scope.for_user(token.user))
      |> assign(:current_user, token.user)
    else
      {:error, reason} ->
        handle_auth_error(conn, reason)
    end
  end

  @doc """
  Plug for requiring specific scopes.

  Usage:
    plug PersonalAccessTokenAuth, scopes: ["read:projects", "write:tasks"]
  """
  def require_scopes(conn, opts) do
    required_scopes = Keyword.get(opts, :scopes, [])
    token = conn.assigns[:current_pat]

    if token && has_required_scopes?(token, required_scopes) do
      conn
    else
      conn
      |> put_status(:forbidden)
      |> json(%{error: "Insufficient permissions", required_scopes: required_scopes})
      |> halt()
    end
  end

  defp extract_token(conn) do
    case get_req_header(conn, "authorization") do
      ["Bearer " <> token] ->
        {:ok, token}

      ["Token " <> token] ->
        {:ok, token}

      _ ->
        case conn.params["access_token"] do
          token when is_binary(token) -> {:ok, token}
          _ -> {:error, :missing_token}
        end
    end
  end

  defp authenticate_token(token_string, ip_address) do
    case Accounts.authenticate_personal_access_token(token_string, ip_address) do
      {:ok, token} -> {:ok, token}
      {:error, reason} -> {:error, reason}
    end
  end

  defp check_scopes(token, opts) do
    required_scopes = Keyword.get(opts, :scopes, [])

    if has_required_scopes?(token, required_scopes) do
      :ok
    else
      {:error, :insufficient_scope}
    end
  end

  defp has_required_scopes?(_token, []), do: true

  defp has_required_scopes?(token, required_scopes) do
    Enum.all?(required_scopes, &PersonalAccessToken.has_scope?(token, &1))
  end

  defp log_authentication(token, conn) do
    log_attrs =
      PersonalAccessTokenLog.log_authentication(
        token.id,
        get_client_ip(conn),
        get_user_agent(conn),
        get_request_id(conn)
      )

    # Log asynchronously to avoid blocking the request
    Task.start(fn ->
      Accounts.log_personal_access_token_usage(log_attrs)
    end)
  end

  defp handle_auth_error(conn, reason) do
    {status, message} =
      case reason do
        :missing_token -> {:unauthorized, "Missing or invalid authorization token"}
        :invalid -> {:unauthorized, "Invalid token"}
        :expired -> {:unauthorized, "Token has expired"}
        :revoked -> {:unauthorized, "Token has been revoked"}
        :ip_not_allowed -> {:forbidden, "IP address not allowed for this token"}
        :insufficient_scope -> {:forbidden, "Insufficient token permissions"}
        _ -> {:unauthorized, "Authentication failed"}
      end

    conn
    |> put_status(status)
    |> json(%{error: message})
    |> halt()
  end

  defp get_client_ip(conn) do
    case get_req_header(conn, "x-forwarded-for") do
      [ip | _] ->
        String.split(ip, ",") |> List.first() |> String.trim()

      [] ->
        case conn.remote_ip do
          {a, b, c, d} ->
            "#{a}.#{b}.#{c}.#{d}"

          {a, b, c, d, e, f, g, h} ->
            [a, b, c, d, e, f, g, h]
            |> Enum.map(&Integer.to_string(&1, 16))
            |> Enum.join(":")

          _ ->
            "unknown"
        end
    end
  end

  defp get_user_agent(conn) do
    case get_req_header(conn, "user-agent") do
      [user_agent | _] -> user_agent
      [] -> "unknown"
    end
  end

  defp get_request_id(conn) do
    case get_req_header(conn, "x-request-id") do
      [request_id | _] -> request_id
      [] -> conn.assigns[:request_id]
    end
  end
end
