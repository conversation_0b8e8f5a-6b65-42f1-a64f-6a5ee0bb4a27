defmodule Codebake.Accounts do
  @moduledoc """
  The Accounts context.
  """

  import Ecto.Query, warn: false
  alias <PERSON><PERSON><PERSON>.Repo

  alias <PERSON>bake.Accounts.{
    User,
    UserToken,
    UserNotifier,
    Account,
    PersonalAccessToken,
    PersonalAccessTokenLog
  }

  alias Codebake.Accounts.AccountMembership

  ## Account functions

  @doc """
  Creates a new account with the given attributes.
  """
  def create_account(attrs \\ %{}) do
    %Account{}
    |> Account.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates an account with the given attributes.
  """
  def update_account(account, attrs) do
    account
    |> Account.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes an account.
  """
  def delete_account(account) do
    Repo.delete(account)
  end

  @doc """
  Gets an account by ID.
  """
  def get_account(id) do
    Repo.get(Account, id)
  end

  @doc """
  Gets an account by ID, raises if not found.
  """
  def get_account!(id) do
    Repo.get!(Account, id)
  end

  @doc """
  Gets an account by slug.
  """
  def get_account_by_slug(slug) do
    Repo.get_by(Account, slug: slug)
  end

  ## Account Memberships

  @doc """
  Gets a membership for a user in an account, or nil.
  """
  @spec get_account_membership(User.t() | nil, Account.t() | nil) :: AccountMembership.t() | nil
  def get_account_membership(%User{id: user_id}, %Account{id: account_id}) do
    Repo.get_by(AccountMembership, user_id: user_id, account_id: account_id)
  end

  def get_account_membership(_, _), do: nil

  @doc """
  Creates an account membership with a role.
  """
  @spec create_account_membership(User.t(), Account.t(), String.t()) ::
          {:ok, AccountMembership.t()} | {:error, Ecto.Changeset.t()}
  def create_account_membership(%User{} = user, %Account{} = account, role \\ "member") do
    %AccountMembership{}
    |> AccountMembership.changeset(%{user_id: user.id, account_id: account.id, role: role})
    |> Repo.insert()
  end

  @doc """
  Ensures a membership exists for a user in an account with at least the given role.

  If a membership exists, updates the role if different. Returns {:ok, membership} or {:error, changeset}.
  """
  @spec ensure_account_membership(User.t(), Account.t(), String.t()) ::
          {:ok, AccountMembership.t()} | {:error, Ecto.Changeset.t()}
  def ensure_account_membership(%User{} = user, %Account{} = account, role \\ "member") do
    case get_account_membership(user, account) do
      nil ->
        create_account_membership(user, account, role)

      %AccountMembership{} = m when m.role == role ->
        {:ok, m}

      %AccountMembership{} = m ->
        m
        |> AccountMembership.changeset(%{role: role})
        |> Repo.update()
    end
  end

  @doc """
  Checks if a user is a member of an account (via AccountMembership only).
  Defensive: returns false if either is nil.
  """
  @spec account_member?(User.t() | nil, Account.t() | nil) :: boolean()
  def account_member?(%User{} = user, %Account{} = account) do
    not is_nil(get_account_membership(user, account))
  end

  def account_member?(_, _), do: false

  @doc """
  Gets account statistics including project counts.
  """
  def get_account_stats(account) do
    project_count =
      from(p in "projects", where: p.account_id == ^account.id) |> Repo.aggregate(:count)

    %{
      project_count: project_count
    }
  end

  @doc """
  Returns the current owner AccountMembership for the account, if any.
  """
  @spec get_account_owner(Account.t()) :: AccountMembership.t() | nil
  def get_account_owner(%Account{id: account_id}) do
    Repo.get_by(AccountMembership, account_id: account_id, role: "owner")
  end

  @doc """
  Promotes a user to Owner of the account. Intended for system flows (e.g., onboarding).
  Demotes any existing owner to Admin. Done within a transaction for atomicity.
  """
  @spec promote_to_owner(User.t(), Account.t()) ::
          {:ok, %{old_owner: AccountMembership.t() | nil, new_owner: AccountMembership.t()}}
          | {:error, Ecto.Changeset.t()}
  def promote_to_owner(%User{} = user, %Account{} = account) do
    Repo.transaction(fn ->
      old_owner = get_account_owner(account)

      # Demote existing owner to admin (if any)
      case old_owner do
        %AccountMembership{} = m ->
          {:ok, _} = m |> AccountMembership.changeset(%{role: "admin"}) |> Repo.update()

        nil ->
          :ok
      end

      # Ensure target has a membership and set to owner
      new_owner =
        case get_account_membership(user, account) do
          nil ->
            {:ok, m} = create_account_membership(user, account, "owner")
            m

          %AccountMembership{} = m ->
            {:ok, m} = m |> AccountMembership.changeset(%{role: "owner"}) |> Repo.update()
            m
        end

      %{old_owner: old_owner, new_owner: new_owner}
    end)
  end

  @doc """
  Transfers ownership from the acting owner to the target user.
  Guardrails:
  - Only the current owner may transfer ownership
  - Target must be a member of the account (creates membership automatically if missing)
  Returns {:ok, %{old_owner: ..., new_owner: ...}} or {:error, reason}.
  """
  @spec transfer_ownership(User.t(), User.t(), Account.t()) ::
          {:ok, %{old_owner: AccountMembership.t(), new_owner: AccountMembership.t()}}
          | {:error, :forbidden | Ecto.Changeset.t()}
  def transfer_ownership(%User{} = acting_user, %User{} = target_user, %Account{} = account) do
    case get_account_owner(account) do
      %AccountMembership{user_id: owner_id} = _m when owner_id == acting_user.id ->
        case promote_to_owner(target_user, account) do
          {:ok, result} -> {:ok, result}
          {:error, reason} -> {:error, reason}
        end

      _ ->
        {:error, :forbidden}
    end
  end

  @doc """
  Checks if a user is a member of a specific project.
  """
  @spec project_member?(User.t() | nil, any()) :: boolean()
  def project_member?(%User{id: user_id}, %{} = project) do
    alias Codebake.Projects.ProjectMembership

    case Map.fetch(project, :id) do
      {:ok, project_id} when not is_nil(project_id) ->
        from(pm in ProjectMembership,
          where: pm.user_id == ^user_id and pm.project_id == ^project_id
        )
        |> Repo.exists?()

      _ ->
        false
    end
  end

  def project_member?(_, _), do: false

  @doc """
  Gets a user's role in an account from AccountMembership only.
  Returns nil if no membership exists.
  """
  @spec get_user_account_role(User.t(), Account.t()) :: String.t() | nil
  def get_user_account_role(%User{} = user, %Account{} = account) do
    case get_account_membership(user, account) do
      %AccountMembership{role: role} when is_binary(role) -> role
      _ -> nil
    end
  end

  @doc """
  Checks if a user needs to complete account setup.

  Returns true if the user has no account or their account has no proper name set.
  """
  def user_needs_account_setup?(%{account_id: nil}), do: true

  def user_needs_account_setup?(%{account_id: account_id}) when not is_nil(account_id) do
    account = get_account!(account_id)
    # Check if account name looks like a generated default
    is_generated_account_name?(account.name)
  end

  def user_needs_account_setup?(_user), do: true

  @doc """
  Gets the user's account, preloaded if it exists.
  """
  def get_user_account(user) do
    case user.account_id do
      nil -> nil
      account_id -> get_account!(account_id)
    end
  end

  # Helper to detect if an account name looks auto-generated
  defp is_generated_account_name?(name) when is_binary(name) do
    # Check if it matches patterns like "<EMAIL>'s Account" or "user-123"
    String.contains?(name, "'s Account") or
      String.contains?(name, "@") or
      String.match?(name, ~r/^[a-z]+-\d+$/)
  end

  defp is_generated_account_name?(_), do: true

  ## Personal Access Token functions

  @doc """
  Creates a new personal access token for a user.
  Returns {:ok, token_struct_with_plain_token} or {:error, changeset}.
  """
  def create_personal_access_token(user, attrs \\ %{}) do
    attrs = Map.put(attrs, :user_id, user.id)

    %PersonalAccessToken{}
    |> PersonalAccessToken.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Lists all personal access tokens for a user.
  """
  def list_personal_access_tokens(user) do
    PersonalAccessToken
    |> where([pat], pat.user_id == ^user.id)
    |> order_by([pat], desc: pat.inserted_at)
    |> Repo.all()
  end

  @doc """
  Gets a personal access token by ID for a specific user.
  """
  def get_personal_access_token(user, token_id) do
    PersonalAccessToken
    |> where([pat], pat.user_id == ^user.id and pat.id == ^token_id)
    |> Repo.one()
  end

  @doc """
  Gets a personal access token by its hashed value.
  Used for authentication.
  """
  def get_personal_access_token_by_hash(token_hash) do
    PersonalAccessToken
    |> where([pat], pat.token_hash == ^token_hash)
    |> preload(:user)
    |> Repo.one()
  end

  @doc """
  Authenticates a request using a personal access token.
  Returns {:ok, token} or {:error, reason}.
  """
  def authenticate_personal_access_token(token_string, ip_address \\ nil) do
    token_hash = PersonalAccessToken.hash_token(token_string)

    case get_personal_access_token_by_hash(token_hash) do
      %PersonalAccessToken{} = token ->
        cond do
          PersonalAccessToken.revoked?(token) ->
            {:error, :revoked}

          PersonalAccessToken.expired?(token) ->
            {:error, :expired}

          not PersonalAccessToken.ip_allowed?(token, ip_address) ->
            {:error, :ip_not_allowed}

          true ->
            # Update usage statistics
            token
            |> PersonalAccessToken.usage_changeset()
            |> Repo.update()

            {:ok, token}
        end

      nil ->
        {:error, :invalid}
    end
  end

  @doc """
  Updates a personal access token.
  """
  def update_personal_access_token(token, attrs) do
    token
    |> PersonalAccessToken.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Revokes a personal access token.
  """
  def revoke_personal_access_token(token, revoked_by_user) do
    token
    |> PersonalAccessToken.revoke_changeset(revoked_by_user)
    |> Repo.update()
  end

  @doc """
  Logs usage of a personal access token.
  """
  def log_personal_access_token_usage(attrs) do
    %PersonalAccessTokenLog{}
    |> PersonalAccessTokenLog.create_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Gets usage logs for a personal access token.
  """
  def get_personal_access_token_logs(token, opts \\ []) do
    limit = Keyword.get(opts, :limit, 100)

    PersonalAccessTokenLog
    |> where([log], log.personal_access_token_id == ^token.id)
    |> order_by([log], desc: log.inserted_at)
    |> limit(^limit)
    |> Repo.all()
  end

  ## Database getters

  @doc """
  Gets a user by email.

  ## Examples

      iex> get_user_by_email("<EMAIL>")
      %User{}

      iex> get_user_by_email("<EMAIL>")
      nil

  """
  def get_user_by_email(email) when is_binary(email) do
    Repo.get_by(User, email: email)
  end

  @doc """
  Gets a user by email and password.

  ## Examples

      iex> get_user_by_email_and_password("<EMAIL>", "correct_password")
      %User{}

      iex> get_user_by_email_and_password("<EMAIL>", "invalid_password")
      nil

  """
  def get_user_by_email_and_password(email, password)
      when is_binary(email) and is_binary(password) do
    user = Repo.get_by(User, email: email)
    if User.valid_password?(user, password), do: user
  end

  @doc """
  Gets a single user.

  Raises `Ecto.NoResultsError` if the User does not exist.

  ## Examples

      iex> get_user!(123)
      %User{}

      iex> get_user!(456)
      ** (Ecto.NoResultsError)

  """
  def get_user!(id), do: Repo.get!(User, id)

  ## User registration

  @doc """
  Registers a user.

  ## Examples

      iex> register_user(%{field: value})
      {:ok, %User{}}

      iex> register_user(%{field: bad_value})
      {:error, %Ecto.Changeset{}}

  """
  def register_user(attrs) do
    %User{}
    |> User.email_changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a user with the given attributes.
  """
  def update_user(user, attrs) do
    user
    |> User.update_changeset(attrs)
    |> Repo.update()
  end

  ## Settings

  @doc """
  Checks whether the user is in sudo mode.

  The user is in sudo mode when the last authentication was done no further
  than 20 minutes ago. The limit can be given as second argument in minutes.
  """
  def sudo_mode?(user, minutes \\ -20)

  def sudo_mode?(%User{authenticated_at: ts}, minutes) when is_struct(ts, DateTime) do
    DateTime.after?(ts, DateTime.utc_now() |> DateTime.add(minutes, :minute))
  end

  def sudo_mode?(_user, _minutes), do: false

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user email.

  See `Codebake.Accounts.User.email_changeset/3` for a list of supported options.

  ## Examples

      iex> change_user_email(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_email(user, attrs \\ %{}, opts \\ []) do
    User.email_changeset(user, attrs, opts)
  end

  @doc """
  Updates the user email using the given token.

  If the token matches, the user email is updated and the token is deleted.
  """
  def update_user_email(user, token) do
    context = "change:#{user.email}"

    Repo.transact(fn ->
      with {:ok, query} <- UserToken.verify_change_email_token_query(token, context),
           %UserToken{sent_to: email} <- Repo.one(query),
           {:ok, user} <- Repo.update(User.email_changeset(user, %{email: email})),
           {_count, _result} <-
             Repo.delete_all(from(UserToken, where: [user_id: ^user.id, context: ^context])) do
        {:ok, user}
      else
        _ -> {:error, :transaction_aborted}
      end
    end)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for changing the user password.

  See `Codebake.Accounts.User.password_changeset/3` for a list of supported options.

  ## Examples

      iex> change_user_password(user)
      %Ecto.Changeset{data: %User{}}

  """
  def change_user_password(user, attrs \\ %{}, opts \\ []) do
    User.password_changeset(user, attrs, opts)
  end

  @doc """
  Updates the user password.

  Returns a tuple with the updated user, as well as a list of expired tokens.

  ## Examples

      iex> update_user_password(user, %{password: ...})
      {:ok, {%User{}, [...]}}

      iex> update_user_password(user, %{password: "too short"})
      {:error, %Ecto.Changeset{}}

  """
  def update_user_password(user, attrs) do
    user
    |> User.password_changeset(attrs)
    |> update_user_and_delete_all_tokens()
  end

  ## Session

  @doc """
  Generates a session token.
  """
  def generate_user_session_token(user) do
    {token, user_token} = UserToken.build_session_token(user)
    Repo.insert!(user_token)
    token
  end

  @doc """
  Gets the user with the given signed token.

  If the token is valid `{user, token_inserted_at}` is returned, otherwise `nil` is returned.
  """
  def get_user_by_session_token(token) do
    {:ok, query} = UserToken.verify_session_token_query(token)
    Repo.one(query)
  end

  @doc """
  Gets the user with the given magic link token.
  """
  def get_user_by_magic_link_token(token) do
    with {:ok, query} <- UserToken.verify_magic_link_token_query(token),
         {user, _token} <- Repo.one(query) do
      user
    else
      _ -> nil
    end
  end

  @doc """
  Logs the user in by magic link.

  There are three cases to consider:

  1. The user has already confirmed their email. They are logged in
     and the magic link is expired.

  2. The user has not confirmed their email and no password is set.
     In this case, the user gets confirmed, logged in, and all tokens -
     including session ones - are expired. In theory, no other tokens
     exist but we delete all of them for best security practices.

  3. The user has not confirmed their email but a password is set.
     This cannot happen in the default implementation but may be the
     source of security pitfalls. See the "Mixing magic link and password registration" section of
     `mix help phx.gen.auth`.
  """
  def login_user_by_magic_link(token) do
    {:ok, query} = UserToken.verify_magic_link_token_query(token)

    case Repo.one(query) do
      # Prevent session fixation attacks by disallowing magic links for unconfirmed users with password
      {%User{confirmed_at: nil, hashed_password: hash}, _token} when not is_nil(hash) ->
        raise """
        magic link log in is not allowed for unconfirmed users with a password set!

        This cannot happen with the default implementation, which indicates that you
        might have adapted the code to a different use case. Please make sure to read the
        "Mixing magic link and password registration" section of `mix help phx.gen.auth`.
        """

      {%User{confirmed_at: nil} = user, _token} ->
        user
        |> User.confirm_changeset()
        |> update_user_and_delete_all_tokens()

      {user, token} ->
        Repo.delete!(token)
        {:ok, {user, []}}

      nil ->
        {:error, :not_found}
    end
  end

  @doc ~S"""
  Delivers the update email instructions to the given user.

  ## Examples

      iex> deliver_user_update_email_instructions(user, current_email, &url(~p"/users/settings/confirm-email/#{&1}"))
      {:ok, %{to: ..., body: ...}}

  """
  def deliver_user_update_email_instructions(%User{} = user, current_email, update_email_url_fun)
      when is_function(update_email_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "change:#{current_email}")

    Repo.insert!(user_token)
    UserNotifier.deliver_update_email_instructions(user, update_email_url_fun.(encoded_token))
  end

  @doc """
  Delivers the magic link login instructions to the given user.
  """
  def deliver_login_instructions(%User{} = user, magic_link_url_fun)
      when is_function(magic_link_url_fun, 1) do
    {encoded_token, user_token} = UserToken.build_email_token(user, "login")
    Repo.insert!(user_token)
    UserNotifier.deliver_login_instructions(user, magic_link_url_fun.(encoded_token))
  end

  @doc """
  Deletes the signed token with the given context.
  """
  def delete_user_session_token(token) do
    Repo.delete_all(from(UserToken, where: [token: ^token, context: "session"]))
    :ok
  end

  ## Token helper

  defp update_user_and_delete_all_tokens(changeset) do
    Repo.transact(fn ->
      with {:ok, user} <- Repo.update(changeset) do
        tokens_to_expire = Repo.all_by(UserToken, user_id: user.id)

        Repo.delete_all(from(t in UserToken, where: t.id in ^Enum.map(tokens_to_expire, & &1.id)))

        {:ok, {user, tokens_to_expire}}
      end
    end)
  end
end
