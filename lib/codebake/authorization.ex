defmodule Codebake.Authorization do
  @moduledoc """
  Authorization policies for CodeBake using Bodyguard.

  This module implements role-based access control (RBAC) for our multi-tenant
  architecture with Account → Project → User hierarchy.

  ## Roles

  - `owner`: Full access to project, can delete project, manage all settings
  - `admin`: Can manage project settings, invite/remove members, manage all tasks
  - `member`: Can create/edit tasks, comment
  - `viewer`: Read-only access to projects and tasks

  ## Usage

      # In controllers/LiveViews
      with :ok <- Bodyguard.permit(Codebake.Authorization, :read_project, current_scope, project) do
        # authorized
      else
        {:error, :unauthorized} -> # handle unauthorized
      end

      # Using helper functions
      if Codebake.Authorization.can?(current_scope, :update_project, project) do
        # authorized
      end
  """

  @behaviour Bodyguard.Policy

  alias Codebake.Accounts.User
  alias Codebake.Projects
  alias Codebake.Projects.{Project, Task}

  # Role hierarchy (higher number = more permissions)
  @role_levels %{
    "viewer" => 1,
    "member" => 2,
    "admin" => 3,
    "owner" => 4
  }

  @doc """
  Main authorization function called by Bodyguard.

  Returns `:ok` if authorized, `{:error, :unauthorized}` if not.
  """
  def authorize(action, %{user: user} = _scope, resource) when not is_nil(user) do
    do_authorize(action, user, resource)
  end

  def authorize(_action, _scope, _resource) do
    {:error, :unauthorized}
  end

  ## Project-level authorization

  defp do_authorize(:read_project, user, %Project{} = project) do
    if project_member?(user, project), do: :ok, else: {:error, :unauthorized}
  end

  defp do_authorize(:update_project, user, %Project{} = project) do
    if has_project_role?(user, project, ["member", "admin", "owner"]),
      do: :ok,
      else: {:error, :unauthorized}
  end

  defp do_authorize(:manage_project_members, user, %Project{} = project) do
    if has_project_role?(user, project, ["admin", "owner"]),
      do: :ok,
      else: {:error, :unauthorized}
  end

  defp do_authorize(:delete_project, user, %Project{} = project) do
    if has_project_role?(user, project, ["owner"]), do: :ok, else: {:error, :unauthorized}
  end

  ## Task-level authorization (updated for project-based system)

  defp do_authorize(:read_tasks, user, %Project{} = project) do
    if project_member?(user, project), do: :ok, else: {:error, :unauthorized}
  end

  defp do_authorize(:create_task, user, %Project{} = project) do
    if has_project_role?(user, project, ["member", "admin", "owner"]),
      do: :ok,
      else: {:error, :unauthorized}
  end

  defp do_authorize(:update_task, user, %Task{} = task) do
    project = Projects.get_project!(task.project_id)

    if has_project_role?(user, project, ["member", "admin", "owner"]),
      do: :ok,
      else: {:error, :unauthorized}
  end

  defp do_authorize(:delete_task, user, %Task{} = task) do
    project = Projects.get_project!(task.project_id)

    if has_project_role?(user, project, ["admin", "owner"]),
      do: :ok,
      else: {:error, :unauthorized}
  end

  ## Fallback
  defp do_authorize(_action, _user, _resource) do
    {:error, :unauthorized}
  end

  ## Helper functions

  @doc """
  Convenience function to check if a user can perform an action.

  Returns `true` if authorized, `false` if not.

  ## Examples

      iex> Codebake.Authorization.can?(current_scope, :read_project, project)
      true

      iex> Codebake.Authorization.can?(current_scope, :delete_project, project)
      false
  """
  def can?(scope, action, resource) do
    case authorize(action, scope, resource) do
      :ok -> true
      {:error, _} -> false
    end
  end

  @doc """
  Get the role levels mapping.
  """
  def role_levels, do: @role_levels

  ## Project-level helper functions

  @doc """
  Check if user is a member of the given project.
  """
  def project_member?(%User{} = user, %Project{} = project) do
    Projects.project_member?(user, project)
  end

  @doc """
  Check if user has one of the specified roles in the project.
  """
  def has_project_role?(%User{} = user, %Project{} = project, required_roles)
      when is_list(required_roles) do
    case Projects.get_user_role_in_project(user, project) do
      nil -> false
      user_role -> user_role in required_roles
    end
  end

  def has_project_role?(%User{} = user, %Project{} = project, required_role)
      when is_binary(required_role) do
    has_project_role?(user, project, [required_role])
  end

  @doc """
  Check if user has at least the minimum role level in the project.

  Role levels: viewer(1) < member(2) < admin(3) < owner(4)
  """
  def has_min_project_role?(%User{} = user, %Project{} = project, min_role) do
    case Projects.get_user_role_in_project(user, project) do
      nil ->
        false

      user_role ->
        user_level = Map.get(@role_levels, user_role, 0)
        min_level = Map.get(@role_levels, min_role, 999)
        user_level >= min_level
    end
  end
end
