defmodule Codebake.Release do
  @moduledoc """
  Release-time helpers. In dev docker setup we call seed/0 from priv/repo/seeds.exs.
  """

  alias Codebake.Seeds.DemoData

  @type seed_result :: :ok | {:error, list()}

  @spec seed() :: seed_result
  def seed do
    IO.puts("🌱 Starting Codebake database seeding...")

    # List of seed modules to run
    seed_modules = [
      DemoData
    ]

    # Run each seed module and collect any errors
    results =
      Enum.map(seed_modules, fn module ->
        try do
          case module.seed() do
            :ok -> :ok
            {:error, reason} -> {:error, module, reason}
          end
        rescue
          error -> {:error, module, error}
        catch
          error -> {:error, module, error}
        end
      end)

    # Check if any seeds failed
    errors = Enum.filter(results, &match?({:error, _, _}, &1))

    case errors do
      [] ->
        IO.puts("🎉 All seeds completed successfully!")
        :ok

      errors ->
        IO.puts("❌ Some seeds failed:")

        Enum.each(errors, fn {:error, module, reason} ->
          IO.puts("  - #{module}: #{inspect(reason)}")
        end)

        {:error, errors}
    end
  end
end
