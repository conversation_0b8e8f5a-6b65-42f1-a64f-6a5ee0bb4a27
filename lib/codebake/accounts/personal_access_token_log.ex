defmodule Codebake.Accounts.PersonalAccessTokenLog do
  @moduledoc """
  PersonalAccessTokenLog schema for auditing PAT usage.

  Tracks all API requests made using Personal Access Tokens for security
  and compliance purposes.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @actions ~w(
    authenticate
    list_projects
    get_project
    create_project
    update_project
    delete_project
    list_tasks
    get_task
    create_task
    update_task
    delete_task
    list_comments
    get_comment
    create_comment
    update_comment
    delete_comment
    create_branch
    create_pr
    webhook_received
  )

  schema "personal_access_token_logs" do
    field :action, :string
    field :ip_address, :string
    field :user_agent, :string
    field :endpoint, :string
    field :method, :string
    field :status_code, :integer
    field :response_time_ms, :integer
    field :request_id, :string
    field :metadata, :map, default: %{}

    belongs_to :personal_access_token, Codebake.Accounts.PersonalAccessToken

    timestamps(type: :utc_datetime, updated_at: false)
  end

  @doc """
  Changeset for creating a new log entry.
  """
  def create_changeset(log, attrs) do
    log
    |> cast(attrs, [
      :personal_access_token_id,
      :action,
      :ip_address,
      :user_agent,
      :endpoint,
      :method,
      :status_code,
      :response_time_ms,
      :request_id,
      :metadata
    ])
    |> validate_required([:personal_access_token_id, :action])
    |> validate_inclusion(:action, @actions)
    |> validate_inclusion(:method, ~w(GET POST PUT PATCH DELETE))
    |> validate_number(:status_code, greater_than: 99, less_than: 600)
    |> validate_number(:response_time_ms, greater_than_or_equal_to: 0)
    |> foreign_key_constraint(:personal_access_token_id)
  end

  @doc """
  Returns all available actions.
  """
  def available_actions, do: @actions

  @doc """
  Creates a log entry for successful authentication.
  """
  def log_authentication(token_id, ip_address, user_agent, request_id \\ nil) do
    %{
      personal_access_token_id: token_id,
      action: "authenticate",
      ip_address: ip_address,
      user_agent: user_agent,
      request_id: request_id,
      status_code: 200
    }
  end

  @doc """
  Creates a log entry for API requests.
  """
  def log_api_request(token_id, action, conn, response_time_ms \\ nil, metadata \\ %{}) do
    %{
      personal_access_token_id: token_id,
      action: action,
      ip_address: get_client_ip(conn),
      user_agent: get_user_agent(conn),
      endpoint: conn.request_path,
      method: conn.method,
      status_code: conn.status,
      response_time_ms: response_time_ms,
      request_id: get_request_id(conn),
      metadata: metadata
    }
  end

  defp get_client_ip(conn) do
    case Plug.Conn.get_req_header(conn, "x-forwarded-for") do
      [ip | _] ->
        String.split(ip, ",") |> List.first() |> String.trim()

      [] ->
        case conn.remote_ip do
          {a, b, c, d} ->
            "#{a}.#{b}.#{c}.#{d}"

          {a, b, c, d, e, f, g, h} ->
            [a, b, c, d, e, f, g, h]
            |> Enum.map(&Integer.to_string(&1, 16))
            |> Enum.join(":")

          _ ->
            "unknown"
        end
    end
  end

  defp get_user_agent(conn) do
    case Plug.Conn.get_req_header(conn, "user-agent") do
      [user_agent | _] -> user_agent
      [] -> "unknown"
    end
  end

  defp get_request_id(conn) do
    case Plug.Conn.get_req_header(conn, "x-request-id") do
      [request_id | _] -> request_id
      [] -> conn.assigns[:request_id]
    end
  end
end
