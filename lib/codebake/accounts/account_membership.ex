defmodule Codebake.Accounts.AccountMembership do
  @moduledoc """
  Links Users to Accounts and stores their account-level role.

  Roles: owner, admin, member, viewer
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}

  @roles ~w(owner admin member viewer)

  schema "account_memberships" do
    field :role, :string, default: "member"

    belongs_to :account, Codebake.Accounts.Account, type: :binary_id
    # Users use integer id in this app
    belongs_to :user, Codebake.Accounts.User, type: :id

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating or updating an account membership.
  """
  def changeset(membership, attrs) do
    membership
    |> cast(attrs, [:account_id, :user_id, :role])
    |> validate_required([:account_id, :user_id, :role])
    |> validate_inclusion(:role, @roles)
    |> unique_constraint([:account_id, :user_id])
    |> foreign_key_constraint(:account_id)
    |> foreign_key_constraint(:user_id)
  end

  @doc """
  Valid role list.
  """
  def valid_roles, do: @roles
end
