defmodule Codebake.Accounts.PersonalAccessToken do
  @moduledoc """
  PersonalAccessToken schema for AI agent authentication.

  Provides secure, scoped access tokens for external AI agents to interact
  with the platform via the MCP (Model Context Protocol) interface.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @available_scopes ~w(
    read:projects
    write:projects
    read:tasks
    write:tasks
    read:comments
    write:comments
    read:teams
    admin:teams
  )

  @default_expiry_days 90

  schema "personal_access_tokens" do
    field :name, :string
    field :token_hash, :string
    field :scopes, {:array, :string}, default: []
    field :expires_at, :utc_datetime
    field :last_used_at, :utc_datetime
    field :usage_count, :integer, default: 0
    field :ip_whitelist, {:array, :string}, default: []
    field :revoked_at, :utc_datetime
    field :metadata, :map, default: %{}

    # Virtual field for the actual token (only available during creation)
    field :token, :string, virtual: true

    belongs_to :user, Codebake.Accounts.User, type: :id
    belongs_to :revoked_by, Codebake.Accounts.User, type: :id
    has_many :logs, Codebake.Accounts.PersonalAccessTokenLog

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new personal access token.
  """
  def create_changeset(token, attrs) do
    token
    |> cast(attrs, [:name, :scopes, :expires_at, :ip_whitelist, :metadata, :user_id])
    |> validate_required([:name, :user_id])
    |> validate_length(:name, min: 3, max: 100)
    |> validate_scopes()
    |> set_default_expiry()
    |> generate_token()
    |> hash_token_changeset()
    |> foreign_key_constraint(:user_id)
  end

  @doc """
  Changeset for updating token metadata (name, scopes, etc).
  """
  def update_changeset(token, attrs) do
    token
    |> cast(attrs, [:name, :scopes, :ip_whitelist, :metadata])
    |> validate_required([:name])
    |> validate_length(:name, min: 3, max: 100)
    |> validate_scopes()
    |> validate_not_revoked()
  end

  @doc """
  Changeset for revoking a token.
  """
  def revoke_changeset(token, revoked_by_user) do
    token
    |> change()
    |> put_change(:revoked_at, DateTime.utc_now() |> DateTime.truncate(:second))
    |> put_change(:revoked_by_id, revoked_by_user.id)
    |> validate_not_revoked()
  end

  @doc """
  Changeset for updating usage statistics.
  """
  def usage_changeset(token) do
    token
    |> change()
    |> put_change(:last_used_at, DateTime.utc_now() |> DateTime.truncate(:second))
    |> put_change(:usage_count, (token.usage_count || 0) + 1)
  end

  @doc """
  Returns all available scopes.
  """
  def available_scopes, do: @available_scopes

  @doc """
  Returns true if the token is expired.
  """
  def expired?(%__MODULE__{expires_at: nil}), do: false

  def expired?(%__MODULE__{expires_at: expires_at}) do
    DateTime.compare(DateTime.utc_now(), expires_at) == :gt
  end

  @doc """
  Returns true if the token is revoked.
  """
  def revoked?(%__MODULE__{revoked_at: nil}), do: false
  def revoked?(%__MODULE__{revoked_at: _}), do: true

  @doc """
  Returns true if the token is active (not expired and not revoked).
  """
  def active?(token) do
    not expired?(token) and not revoked?(token)
  end

  @doc """
  Returns true if the token has the given scope.
  """
  def has_scope?(token, scope) do
    scope in (token.scopes || [])
  end

  @doc """
  Returns true if the IP address is allowed for this token.
  """
  def ip_allowed?(%__MODULE__{ip_whitelist: []}, _ip), do: true

  def ip_allowed?(%__MODULE__{ip_whitelist: whitelist}, ip) do
    ip in whitelist
  end

  @doc """
  Generates a secure random token.
  """
  def generate_secure_token do
    :crypto.strong_rand_bytes(32) |> Base.url_encode64(padding: false)
  end

  @doc """
  Hashes a token for secure storage.
  """
  def hash_token(token) when is_binary(token) do
    :crypto.hash(:sha256, token) |> Base.encode16(case: :lower)
  end

  defp validate_scopes(changeset) do
    case get_change(changeset, :scopes) do
      nil ->
        changeset

      scopes when is_list(scopes) ->
        invalid_scopes = scopes -- @available_scopes

        if invalid_scopes == [] do
          changeset
        else
          add_error(
            changeset,
            :scopes,
            "contains invalid scopes: #{Enum.join(invalid_scopes, ", ")}"
          )
        end

      _ ->
        add_error(changeset, :scopes, "must be a list")
    end
  end

  defp set_default_expiry(changeset) do
    case get_field(changeset, :expires_at) do
      nil ->
        default_expiry = DateTime.utc_now() |> DateTime.add(@default_expiry_days, :day)
        put_change(changeset, :expires_at, default_expiry)

      _ ->
        changeset
    end
  end

  defp generate_token(changeset) do
    if changeset.valid? do
      token = generate_secure_token()
      put_change(changeset, :token, token)
    else
      changeset
    end
  end

  defp hash_token_changeset(changeset) do
    case get_change(changeset, :token) do
      nil ->
        changeset

      token ->
        hashed = hash_token(token)
        put_change(changeset, :token_hash, hashed)
    end
  end

  defp validate_not_revoked(changeset) do
    if get_field(changeset, :revoked_at) do
      add_error(changeset, :base, "token is already revoked")
    else
      changeset
    end
  end
end
