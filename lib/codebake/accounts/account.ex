defmodule Codebake.Accounts.Account do
  @moduledoc """
  Account schema representing the billing boundary in our multi-tenant architecture.

  An Account can have multiple Projects and serves as the subscription/billing entity.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @subscription_statuses ~w(trial active past_due canceled)
  @subscription_tiers ~w(freemium team business enterprise)

  schema "accounts" do
    field :name, :string
    field :slug, :string
    field :billing_email, :string
    field :subscription_status, :string, default: "trial"
    field :subscription_tier, :string, default: "freemium"
    field :trial_ends_at, :utc_datetime
    field :settings, :map, default: %{}

    has_many :users, Codebake.Accounts.User
    has_many :projects, Codebake.Projects.Project

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new account.
  """
  def create_changeset(account, attrs) do
    account
    |> cast(attrs, [:name, :billing_email, :subscription_tier])
    |> validate_required([:name])
    |> validate_format(:billing_email, ~r/^[^@,;\s]+@[^@,;\s]+$/,
      message: "must be a valid email address"
    )
    |> validate_inclusion(:subscription_tier, @subscription_tiers)
    |> generate_slug()
    |> unique_constraint(:slug)
    |> set_trial_period()
  end

  @doc """
  Changeset for updating account settings.
  """
  def update_changeset(account, attrs) do
    account
    |> cast(attrs, [:name, :billing_email, :settings])
    |> validate_required([:name])
    |> validate_format(:billing_email, ~r/^[^@,;\s]+@[^@,;\s]+$/,
      message: "must be a valid email address"
    )
    |> generate_slug()
    |> unique_constraint(:slug)
  end

  @doc """
  Changeset for updating subscription information.
  """
  def subscription_changeset(account, attrs) do
    account
    |> cast(attrs, [:subscription_status, :subscription_tier, :trial_ends_at])
    |> validate_inclusion(:subscription_status, @subscription_statuses)
    |> validate_inclusion(:subscription_tier, @subscription_tiers)
  end

  defp generate_slug(changeset) do
    case get_change(changeset, :name) do
      nil ->
        changeset

      name ->
        base_slug =
          name
          |> String.downcase()
          |> String.replace(~r/[^a-z0-9\-]/, "-")
          |> String.replace(~r/-+/, "-")
          |> String.trim("-")

        # Add a 3-digit number to ensure slug uniqueness
        three_digit_number = Enum.random(100..999)
        slug = "#{base_slug}-#{three_digit_number}"

        put_change(changeset, :slug, slug)
    end
  end

  defp set_trial_period(changeset) do
    if get_field(changeset, :subscription_status) == "trial" and
         get_field(changeset, :trial_ends_at) == nil do
      trial_end = DateTime.utc_now() |> DateTime.add(14, :day) |> DateTime.truncate(:second)
      put_change(changeset, :trial_ends_at, trial_end)
    else
      changeset
    end
  end

  @doc """
  Returns true if the account is on a trial that has expired.
  """
  def trial_expired?(%__MODULE__{subscription_status: "trial", trial_ends_at: trial_ends_at})
      when not is_nil(trial_ends_at) do
    DateTime.compare(DateTime.utc_now(), trial_ends_at) == :gt
  end

  def trial_expired?(_account), do: false

  @doc """
  Returns true if the account has an active subscription.
  """
  def active_subscription?(%__MODULE__{subscription_status: status}) do
    status in ["active", "trial"]
  end
end
