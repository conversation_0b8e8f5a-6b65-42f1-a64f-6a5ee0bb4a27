defmodule Codebake.Projects.ProjectMembership do
  @moduledoc """
  ProjectMembership schema representing the many-to-many relationship between Projects and Users.

  Includes role-based permissions: owner, admin, member, viewer.
  This replaces the Team-based authorization system with direct project-level access control.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  @roles ~w(owner admin member viewer)

  schema "project_memberships" do
    field :role, :string, default: "member"
    field :invited_at, :utc_datetime
    field :joined_at, :utc_datetime

    belongs_to :project, Codebake.Projects.Project
    belongs_to :user, Codebake.Accounts.User, type: :id
    belongs_to :invited_by, Codebake.Accounts.User, type: :id

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new project membership (invitation).
  """
  def invite_changeset(membership, attrs) do
    membership
    |> cast(attrs, [:project_id, :user_id, :role, :invited_by_id])
    |> validate_required([:project_id, :user_id, :role])
    |> validate_inclusion(:role, @roles)
    |> unique_constraint([:project_id, :user_id])
    |> foreign_key_constraint(:project_id)
    |> foreign_key_constraint(:user_id)
    |> foreign_key_constraint(:invited_by_id)
    |> put_change(:invited_at, DateTime.utc_now() |> DateTime.truncate(:second))
  end

  @doc """
  Changeset for accepting an invitation (joining a project).
  """
  def join_changeset(membership) do
    membership
    |> change()
    |> put_change(:joined_at, DateTime.utc_now() |> DateTime.truncate(:second))
  end

  @doc """
  Changeset for updating a membership role.
  """
  def role_changeset(membership, attrs) do
    membership
    |> cast(attrs, [:role])
    |> validate_required([:role])
    |> validate_inclusion(:role, @roles)
  end

  @doc """
  Returns the list of valid roles.
  """
  def valid_roles, do: @roles

  @doc """
  Check if a role has sufficient permissions compared to another role.

  Role hierarchy: viewer < member < admin < owner
  """
  def role_sufficient?(user_role, required_role) do
    role_levels = %{
      "viewer" => 1,
      "member" => 2,
      "admin" => 3,
      "owner" => 4
    }

    Map.get(role_levels, user_role, 0) >= Map.get(role_levels, required_role, 0)
  end

  @doc """
  Check if a role can manage another role.

  - owners can manage all roles
  - admins can manage members and viewers
  - members and viewers cannot manage anyone
  """
  def can_manage_role?(manager_role, target_role) do
    case manager_role do
      "owner" -> true
      "admin" -> target_role in ["member", "viewer"]
      _ -> false
    end
  end
end
