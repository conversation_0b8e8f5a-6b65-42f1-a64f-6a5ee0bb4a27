defmodule Codebake.Projects.Project do
  @moduledoc """
  Project schema representing a project within a team.

  Projects belong to Teams and contain Tasks. Each project has a custom
  task prefix for generating human-readable task identifiers (e.g., "PROJ-1", "FEAT-42").
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, UUIDv7, autogenerate: true}
  @foreign_key_type UUIDv7

  @project_statuses ~w(planning active on_hold completed archived)
  @default_task_prefix "TASK"

  schema "projects" do
    field :name, :string
    field :description, :string
    field :task_prefix, :string, default: @default_task_prefix
    field :status, :string, default: "planning"
    field :settings, :map, default: %{}
    field :task_counter, :integer, default: 0

    belongs_to :account, Codebake.Accounts.Account, type: :binary_id
    belongs_to :created_by, Codebake.Accounts.User, type: :id
    has_many :tasks, Codebake.Projects.Task
    has_many :project_memberships, Codebake.Projects.ProjectMembership
    has_many :users, through: [:project_memberships, :user]

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new project.
  """
  def create_changeset(project, attrs) do
    project
    |> cast(attrs, [:name, :description, :task_prefix, :account_id, :created_by_id])
    |> validate_required([:name, :account_id, :created_by_id])
    |> validate_length(:name, min: 2, max: 100)
    |> validate_length(:description, max: 1000)
    |> validate_task_prefix()
    |> unique_constraint([:account_id, :name])
    |> unique_constraint([:account_id, :task_prefix])
    |> foreign_key_constraint(:account_id)
    |> foreign_key_constraint(:created_by_id)
  end

  @doc """
  Changeset for updating project information.
  """
  def update_changeset(project, attrs) do
    project
    |> cast(attrs, [:name, :description, :status, :settings])
    |> validate_required([:name])
    |> validate_length(:name, min: 2, max: 100)
    |> validate_length(:description, max: 1000)
    |> validate_inclusion(:status, @project_statuses)
    |> unique_constraint([:account_id, :name])
  end

  @doc """
  Changeset for updating the task prefix (admin only).
  """
  def task_prefix_changeset(project, attrs) do
    project
    |> cast(attrs, [:task_prefix])
    |> validate_task_prefix()
    |> unique_constraint([:account_id, :task_prefix])
  end

  @doc """
  Changeset for incrementing the task counter.
  """
  def increment_task_counter_changeset(project) do
    project
    |> change()
    |> put_change(:task_counter, (project.task_counter || 0) + 1)
  end

  @doc """
  Returns the next task number for this project.
  """
  def next_task_number(%__MODULE__{task_counter: counter}) do
    (counter || 0) + 1
  end

  @doc """
  Generates a task identifier for this project (e.g., "PROJ-42").
  """
  def generate_task_identifier(%__MODULE__{task_prefix: prefix}, task_number) do
    "#{prefix}-#{task_number}"
  end

  @doc """
  Returns true if the project is active (not archived or completed).
  """
  def active?(%__MODULE__{status: status}) do
    status in ~w(planning active on_hold)
  end

  @doc """
  Returns true if the project allows new tasks to be created.
  """
  def accepts_tasks?(%__MODULE__{status: status}) do
    status in ~w(planning active)
  end

  defp validate_task_prefix(changeset) do
    changeset
    |> validate_required([:task_prefix])
    |> validate_length(:task_prefix, min: 2, max: 10)
    |> validate_format(:task_prefix, ~r/^[A-Z][A-Z0-9]*$/,
      message: "must start with a letter and contain only uppercase letters and numbers"
    )
  end
end
