defmodule Codebake.Projects.Task do
  @moduledoc """
  Task schema representing individual tasks within projects.

  Tasks belong to Projects and can be assigned to Users. They support
  simple blocking relationships via parent_task_id and have a status workflow.
  """

  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, UUIDv7, autogenerate: true}
  @foreign_key_type UUIDv7

  @task_statuses ~w(todo in_progress blocked done)
  @task_priorities ~w(low medium high urgent)

  schema "tasks" do
    field :title, :string
    field :description, :string
    field :status, :string, default: "todo"
    field :priority, :string, default: "medium"
    field :task_number, :integer
    field :task_identifier, :string
    field :position, :integer, default: 0
    field :due_date, :utc_datetime
    field :completed_at, :utc_datetime
    field :estimated_hours, :decimal
    field :actual_hours, :decimal
    field :tags, {:array, :string}, default: []
    field :metadata, :map, default: %{}
    field :phase_id, :binary_id

    belongs_to :project, Codebake.Projects.Project
    belongs_to :assignee, Codebake.Accounts.User, type: :id
    belongs_to :created_by, Codebake.Accounts.User, type: :id
    belongs_to :parent_task, __MODULE__, foreign_key: :parent_task_id

    has_many :subtasks, __MODULE__, foreign_key: :parent_task_id

    timestamps(type: :utc_datetime)
  end

  @doc """
  Changeset for creating a new task.
  """
  def create_changeset(task, attrs) do
    task
    |> cast(attrs, [
      :title,
      :description,
      :priority,
      :due_date,
      :estimated_hours,
      :tags,
      :project_id,
      :assignee_id,
      :created_by_id,
      :parent_task_id,
      :position,
      :status,
      :phase_id
    ])
    |> validate_required([:title, :project_id, :created_by_id, :phase_id])
    |> validate_length(:title, min: 2, max: 200)
    |> validate_length(:description, max: 2000)
    |> validate_inclusion(:priority, @task_priorities)
    |> validate_inclusion(:status, @task_statuses)
    |> validate_number(:estimated_hours, greater_than: 0)
    |> validate_parent_task()
    |> foreign_key_constraint(:project_id)
    |> foreign_key_constraint(:assignee_id)
    |> foreign_key_constraint(:created_by_id)
    |> foreign_key_constraint(:parent_task_id)
  end

  @doc """
  Changeset for updating task information.
  """
  def update_changeset(task, attrs) do
    task
    |> cast(attrs, [
      :title,
      :description,
      :priority,
      :due_date,
      :estimated_hours,
      :actual_hours,
      :tags,
      :assignee_id,
      :parent_task_id,
      :phase_id
    ])
    |> validate_required([:title])
    |> validate_length(:title, min: 2, max: 200)
    |> validate_length(:description, max: 2000)
    |> validate_inclusion(:priority, @task_priorities)
    |> validate_number(:estimated_hours, greater_than: 0)
    |> validate_number(:actual_hours, greater_than: 0)
    |> validate_parent_task()
    |> foreign_key_constraint(:assignee_id)
    |> foreign_key_constraint(:parent_task_id)
  end

  @doc """
  Changeset for updating task status.
  """
  def status_changeset(task, status) do
    task
    |> change(status: status)
    |> validate_inclusion(:status, @task_statuses)
    |> maybe_set_completed_at()
  end

  @doc """
  Changeset for setting task number and identifier.
  """
  def set_task_number_changeset(task, task_number, task_identifier) do
    task
    |> change()
    |> put_change(:task_number, task_number)
    |> put_change(:task_identifier, task_identifier)
    |> unique_constraint(:task_number, name: :tasks_project_id_task_number_index)
    |> unique_constraint(:task_identifier, name: :tasks_project_id_task_identifier_index)
  end

  @doc """
  Returns true if the task is completed.
  """
  def completed?(%__MODULE__{status: "done"}), do: true
  def completed?(_task), do: false

  @doc """
  Returns true if the task is blocked.
  """
  def blocked?(%__MODULE__{status: "blocked"}), do: true
  def blocked?(_task), do: false

  @doc """
  Returns true if the task is in progress.
  """
  def in_progress?(%__MODULE__{status: "in_progress"}), do: true
  def in_progress?(_task), do: false

  @doc """
  Returns true if the task is overdue.
  """
  def overdue?(%__MODULE__{due_date: nil}), do: false

  def overdue?(%__MODULE__{due_date: due_date, status: status}) do
    status != "done" and DateTime.compare(DateTime.utc_now(), due_date) == :gt
  end

  @doc """
  Returns true if the task has subtasks.
  """
  def has_subtasks?(%__MODULE__{} = task) do
    Ecto.assoc_loaded?(task.subtasks) and not Enum.empty?(task.subtasks)
  end

  @doc """
  Returns true if the task is a subtask.
  """
  def subtask?(%__MODULE__{parent_task_id: nil}), do: false
  def subtask?(%__MODULE__{parent_task_id: _}), do: true

  defp validate_parent_task(changeset) do
    case get_change(changeset, :parent_task_id) do
      nil ->
        changeset

      parent_id ->
        # Ensure parent task exists and is in the same project
        if get_field(changeset, :id) == parent_id do
          add_error(changeset, :parent_task_id, "cannot be the same as the task itself")
        else
          changeset
        end
    end
  end

  defp maybe_set_completed_at(changeset) do
    case get_change(changeset, :status) do
      "done" ->
        put_change(changeset, :completed_at, DateTime.utc_now() |> DateTime.truncate(:second))

      _ ->
        put_change(changeset, :completed_at, nil)
    end
  end
end
