defmodule Codebake.MigrationHelpers do
  @moduledoc """
  Helper functions for project system verification.

  These functions help verify the current state of the project-based system.
  """

  import Ecto.Query
  alias <PERSON>bake.Repo

  @doc """
  Verify the current project system state.

  This function checks that:
  1. All projects have account_id set
  2. Project memberships are properly configured
  3. Roles are distributed correctly
  """
  def verify_project_system do
    IO.puts("🔍 Verifying project system...")

    # Check 1: All projects should have account_id
    projects_without_account =
      from(p in "projects", where: is_nil(p.account_id), select: count())
      |> Repo.one()

    if projects_without_account > 0 do
      IO.puts("❌ Found #{projects_without_account} projects without account_id")
    else
      IO.puts("✅ All projects have account_id set")
    end

    # Check 2: Project membership statistics
    project_memberships_count =
      from(pm in "project_memberships", select: count())
      |> Repo.one()

    projects_count =
      from(p in "projects", select: count())
      |> Repo.one()

    accounts_count =
      from(a in "accounts", select: count())
      |> Repo.one()

    IO.puts("📊 System Statistics:")
    IO.puts("   Accounts: #{accounts_count}")
    IO.puts("   Projects: #{projects_count}")
    IO.puts("   Project memberships: #{project_memberships_count}")

    # Check 3: Verify role distribution
    role_distribution =
      from(pm in "project_memberships",
        group_by: pm.role,
        select: {pm.role, count()}
      )
      |> Repo.all()

    IO.puts("📈 Role distribution in project memberships:")

    Enum.each(role_distribution, fn {role, count} ->
      IO.puts("   #{role}: #{count}")
    end)

    # Check 4: Projects per account
    projects_per_account =
      from(a in "accounts",
        left_join: p in "projects",
        on: p.account_id == a.id,
        group_by: [a.id, a.name],
        select: {a.name, count(p.id)}
      )
      |> Repo.all()

    IO.puts("🏢 Projects per account:")

    Enum.each(projects_per_account, fn {account_name, project_count} ->
      IO.puts("   #{account_name}: #{project_count} projects")
    end)

    IO.puts("✅ Project system verification complete!")
  end

  @doc """
  Legacy function name for backward compatibility.
  """
  def verify_migration do
    IO.puts("⚠️  verify_migration/0 is deprecated. Use verify_project_system/0 instead.")
    verify_project_system()
  end
end
