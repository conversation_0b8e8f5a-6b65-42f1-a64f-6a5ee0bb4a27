defmodule Codebake.Seeds.DemoData do
  @moduledoc """
  Seeds demo data for the Codebake application.

  Creates:
  - Admin user (<EMAIL>)
  - Mediastable account
  - Dr<PERSON> <PERSON>'s reanimation project
  - Sample tasks showing project half-done state
  """

  alias <PERSON>bake.Repo
  alias <PERSON><PERSON><PERSON>.Accounts
  alias <PERSON><PERSON><PERSON>.Projects
  alias <PERSON><PERSON>ke.Accounts.{User, Account}
  alias Codebake.Projects.Project

  @admin_email "<EMAIL>"
  @account_name "Mediastable"
  @account_slug "mediastable"
  @project_name "Reanimation Research Project"
  @project_description """
  A comprehensive research project to unlock the secrets of reanimation.

  This ambitious undertaking seeks to bridge the gap between life and death,
  utilizing cutting-edge scientific methods and ancient alchemical wisdom.
  The project encompasses multiple phases from theoretical research to
  practical experimentation.
  """

  def seed do
    IO.puts("🧪 Seeding demo data for <PERSON><PERSON>'s laboratory...")

    with {:ok, account} <- ensure_account(),
         {:ok, admin_user} <- ensure_admin_user(account),
         {:ok, project} <- ensure_reanimation_project(account, admin_user),
         {:ok, _tasks} <- ensure_project_tasks(project, admin_user) do
      IO.puts("✅ Demo data seeding completed successfully!")
      :ok
    else
      {:error, reason} ->
        IO.puts("❌ Demo data seeding failed: #{inspect(reason)}")
        {:error, reason}
    end
  end

  defp ensure_account do
    case Repo.get_by(Account, slug: @account_slug) do
      nil ->
        IO.puts("  Creating Mediastable account...")

        attrs = %{
          "name" => @account_name,
          "slug" => @account_slug,
          "billing_email" => @admin_email,
          "subscription_status" => "active",
          "subscription_tier" => "team",
          "trial_ends_at" => DateTime.utc_now() |> DateTime.add(365, :day),
          "settings" => %{
            "laboratory_access" => true,
            "experiment_permissions" => ["basic", "advanced", "forbidden"]
          }
        }

        Accounts.create_account(attrs)

      account ->
        IO.puts("  Mediastable account already exists")
        {:ok, account}
    end
  end

  defp ensure_admin_user(account) do
    case Repo.get_by(User, email: @admin_email) do
      nil ->
        IO.puts("  Creating admin user...")

        # Create user first
        user_attrs = %{
          email: @admin_email,
          name: "Dr. Victor Frankenstein",
          timezone: "Europe/Geneva",
          settings: %{
            "laboratory_role" => "chief_scientist",
            "security_clearance" => "maximum",
            "preferred_experiments" => ["reanimation", "bioelectricity", "consciousness_transfer"]
          }
        }

        with {:ok, user} <- Accounts.register_user(user_attrs),
             # Confirm the user immediately for demo purposes
             {:ok, user} <- Accounts.update_user(user, %{confirmed_at: DateTime.utc_now()}),
             # Associate with account
             {:ok, user} <- Accounts.update_user(user, %{account_id: account.id}),
             # Create account membership with admin role
             {:ok, _membership} <- Accounts.create_account_membership(user, account, "admin") do
          {:ok, user}
        end

      user ->
        IO.puts("  Admin user already exists")
        {:ok, user}
    end
  end

  defp ensure_reanimation_project(account, admin_user) do
    case Repo.get_by(Project, name: @project_name, account_id: account.id) do
      nil ->
        IO.puts("  Creating Dr. Frankenstein's reanimation project...")

        project_attrs = %{
          "name" => @project_name,
          "description" => @project_description,
          "task_prefix" => "REANM",
          "status" => "active",
          "settings" => %{
            "laboratory_location" => "Castle Frankenstein",
            "security_level" => "classified",
            "experiment_type" => "bioelectrical_reanimation",
            "ethics_approval" => "pending"
          }
        }

        Projects.create_project(account, admin_user, project_attrs)

      project ->
        IO.puts("  Reanimation project already exists")
        {:ok, project}
    end
  end

  defp ensure_project_tasks(project, admin_user) do
    existing_tasks = Projects.list_tasks(project)

    if Enum.empty?(existing_tasks) do
      IO.puts("  Creating sample tasks for reanimation project...")
      create_sample_tasks(project, admin_user)
    else
      IO.puts("  Project tasks already exist")
      {:ok, existing_tasks}
    end
  end

  defp create_sample_tasks(project, admin_user) do
    # Define tasks that show the project is about half done
    tasks_data = [
      # Completed tasks (research phase)
      %{
        "title" => "Research historical reanimation attempts",
        "description" =>
          "Study the works of Paracelsus, Agrippa, and other alchemical masters to understand previous attempts at reanimation.",
        "status" => "done",
        "priority" => "high",
        "estimated_hours" => Decimal.new("40"),
        "actual_hours" => Decimal.new("38"),
        "completed_at" => days_ago(45),
        "tags" => ["research", "historical", "alchemy"]
      },
      %{
        "title" => "Acquire laboratory equipment",
        "description" =>
          "Procure essential equipment: galvanic apparatus, copper conductors, zinc plates, and preservation chemicals.",
        "status" => "done",
        "priority" => "urgent",
        "estimated_hours" => Decimal.new("20"),
        "actual_hours" => Decimal.new("25"),
        "completed_at" => days_ago(40),
        "tags" => ["procurement", "equipment", "galvanic"]
      },
      %{
        "title" => "Study bioelectricity principles",
        "description" =>
          "Deep dive into Galvani's experiments with frog legs and electrical stimulation of muscle tissue.",
        "status" => "done",
        "priority" => "high",
        "estimated_hours" => Decimal.new("60"),
        "actual_hours" => Decimal.new("55"),
        "completed_at" => days_ago(35),
        "tags" => ["research", "bioelectricity", "galvani"]
      },
      %{
        "title" => "Establish secure laboratory space",
        "description" =>
          "Convert the castle's highest tower into a fully equipped laboratory with proper ventilation and electrical systems.",
        "status" => "done",
        "priority" => "medium",
        "estimated_hours" => Decimal.new("80"),
        "actual_hours" => Decimal.new("95"),
        "completed_at" => days_ago(30),
        "tags" => ["infrastructure", "laboratory", "security"]
      },
      %{
        "title" => "Source anatomical specimens",
        "description" =>
          "Acquire fresh specimens for initial experiments. Must be obtained through... legitimate channels.",
        "status" => "done",
        "priority" => "urgent",
        "estimated_hours" => Decimal.new("15"),
        "actual_hours" => Decimal.new("12"),
        "completed_at" => days_ago(25),
        "tags" => ["specimens", "anatomy", "procurement"]
      },

      # In progress tasks (experimentation phase)
      %{
        "title" => "Develop reanimation serum",
        "description" =>
          "Create the perfect chemical compound to restore life force. Current formula shows promise but needs refinement.",
        "status" => "in_progress",
        "priority" => "urgent",
        "estimated_hours" => Decimal.new("100"),
        "actual_hours" => Decimal.new("65"),
        "tags" => ["chemistry", "serum", "life-force"]
      },
      %{
        "title" => "Perfect electrical stimulation technique",
        "description" =>
          "Fine-tune the precise voltage and timing required to restart biological functions without causing tissue damage.",
        "status" => "in_progress",
        "priority" => "high",
        "estimated_hours" => Decimal.new("75"),
        "actual_hours" => Decimal.new("40"),
        "tags" => ["bioelectricity", "technique", "precision"]
      },

      # Blocked task
      %{
        "title" => "Obtain ethics committee approval",
        "description" =>
          "Secure formal approval from the University's ethics committee for human reanimation experiments.",
        "status" => "blocked",
        "priority" => "medium",
        "estimated_hours" => Decimal.new("10"),
        "tags" => ["ethics", "approval", "bureaucracy"]
      },

      # Todo tasks (future phases)
      %{
        "title" => "Conduct first reanimation attempt",
        "description" =>
          "Apply all research and preparation to attempt the first full reanimation of a human subject.",
        "status" => "todo",
        "priority" => "urgent",
        "estimated_hours" => Decimal.new("50"),
        "due_date" => days_from_now(30),
        "tags" => ["experiment", "reanimation", "milestone"]
      },
      %{
        "title" => "Monitor subject vital signs",
        "description" =>
          "Establish continuous monitoring of reanimated subject's biological functions and consciousness levels.",
        "status" => "todo",
        "priority" => "high",
        "estimated_hours" => Decimal.new("40"),
        "due_date" => days_from_now(35),
        "tags" => ["monitoring", "vitals", "consciousness"]
      },
      %{
        "title" => "Document experimental results",
        "description" =>
          "Comprehensive documentation of all procedures, results, and observations for future reference.",
        "status" => "todo",
        "priority" => "medium",
        "estimated_hours" => Decimal.new("30"),
        "due_date" => days_from_now(45),
        "tags" => ["documentation", "results", "analysis"]
      },
      %{
        "title" => "Prepare for public presentation",
        "description" =>
          "Ready the findings for presentation to the scientific community. The world must know of this breakthrough!",
        "status" => "todo",
        "priority" => "low",
        "estimated_hours" => Decimal.new("25"),
        "due_date" => days_from_now(60),
        "tags" => ["presentation", "publication", "fame"]
      }
    ]

    # Create all tasks
    tasks =
      tasks_data
      |> Enum.with_index(1)
      |> Enum.map(fn {task_attrs, _index} ->
        case Projects.create_task(project, admin_user, task_attrs) do
          {:ok, task} ->
            task

          {:error, changeset} ->
            IO.puts(
              "    ⚠️  Failed to create task '#{task_attrs["title"]}': #{inspect(changeset.errors)}"
            )

            nil
        end
      end)
      |> Enum.reject(&is_nil/1)

    IO.puts("    ✅ Created #{length(tasks)} tasks")
    {:ok, tasks}
  end

  defp days_ago(days) do
    DateTime.utc_now()
    |> DateTime.add(-days * 24 * 60 * 60, :second)
    |> DateTime.truncate(:second)
  end

  defp days_from_now(days) do
    DateTime.utc_now()
    |> DateTime.add(days * 24 * 60 * 60, :second)
    |> DateTime.truncate(:second)
  end
end
