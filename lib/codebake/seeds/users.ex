defmodule Codebake.Seeds.Users do
  @moduledoc """
  Seeds for users with different roles in organizations.
  """

  # alias <PERSON>bake.Repo
  # alias Codebake.Accounts.{User, OrgUserRole}
  # alias Codebake.Seeds.{Roles, Orgs}
  # import Ecto.Query
  # import Ecto.Query

  # # System users (misfitlabs.vc) - not linked to any organization
  # @system_users [
  #   %{
  #     email: "<EMAIL>",
  #     password: "ASDFasdf123!",
  #     first_name: "System",
  #     last_name: "Admin",
  #     gender: "prefer_not_to_say",
  #     date_of_birth: ~D[1985-03-15],
  #     role_name: "app_admin"
  #   },
  #   %{
  #     email: "<EMAIL>",
  #     password: "ASDFasdf123!",
  #     first_name: "System",
  #     last_name: "<PERSON>eloper",
  #     gender: "male",
  #     date_of_birth: ~D[1990-07-22],
  #     role_name: "admin"
  #   },
  #   %{
  #     email: "<EMAIL>",
  #     password: "ASDFasdf123!",
  #     first_name: "System",
  #     last_name: "Support",
  #     gender: "female",
  #     date_of_birth: ~D[1988-11-08],
  #     role_name: "navigator"
  #   }
  # ]

  # def seed do
  #   IO.puts("Seeding users...")

  #   # Seed system users (misfitlabs.vc)
  #   IO.puts("  Seeding system users (misfitlabs.vc)...")
  #   Enum.each(@system_users, &create_system_user/1)

  #   # Seed CodebakeHR system users
  #   IO.puts("  Seeding CodebakeHR system users...")
  #   Enum.each(@codebake_users, &create_system_user/1)

  #   # Seed organization users
  #   IO.puts("  Seeding organization users...")
  #   Enum.each(@org_users, &create_org_user/1)

  #   IO.puts("Users seeding completed.")
  # end

  # def get_admin_user do
  #   # Get the first admin user for audit logging purposes
  #   admin_role = Roles.get_role_by_name("admin")

  #   from(u in User,
  #     join: our in OrgUserRole,
  #     on: our.user_id == u.id,
  #     where: our.role_id == ^admin_role.id and is_nil(our.org_id),
  #     limit: 1
  #   )
  #   |> Repo.one()
  # end

  # # Create a system user (not linked to any organization)
  # defp create_system_user(user_attrs) do
  #   {role_name, user_attrs} = Map.pop!(user_attrs, :role_name)

  #   case Repo.get_by(User, email: user_attrs.email) do
  #     nil ->
  #       # Create the user
  #       user =
  #         %User{}
  #         |> User.update_changeset(user_attrs)
  #         |> Repo.insert!()

  #       # Get the system role and assign it to the user
  #       role = Roles.get_role_by_name(role_name)

  #       # Create the org-user-role association (with nil org_id for system users)
  #       %OrgUserRole{}
  #       |> OrgUserRole.changeset(%{
  #         org_id: nil,
  #         user_id: user.id,
  #         role_id: role.id
  #       })
  #       |> Repo.insert!()

  #       IO.puts("    ✓ Created system user: #{user.email} (#{role_name})")

  #     existing_user ->
  #       # Update the existing user with new data
  #       _updated_user =
  #         existing_user
  #         |> User.update_changeset(user_attrs)
  #         |> Repo.update!()

  #       # Check if user already has the role assigned
  #       role = Roles.get_role_by_name(role_name)

  #       case Repo.one(
  #              from our in OrgUserRole,
  #                where:
  #                  our.user_id == ^existing_user.id and our.role_id == ^role.id and
  #                    is_nil(our.org_id)
  #            ) do
  #         nil ->
  #           # Assign the role to the existing user
  #           %OrgUserRole{}
  #           |> OrgUserRole.changeset(%{
  #             org_id: nil,
  #             user_id: existing_user.id,
  #             role_id: role.id
  #           })
  #           |> Repo.insert!()

  #           IO.puts(
  #             "    ✓ Updated and assigned #{role_name} role to existing system user: #{existing_user.email}"
  #           )

  #         _existing_role ->
  #           IO.puts("    ✓ Updated existing system user: #{existing_user.email} (#{role_name})")
  #       end
  #   end
  # end

  # # Create an organization user (linked to a specific organization)
  # defp create_org_user(user_attrs) do
  #   {org_name, role_name, status, employee_id, user_attrs} =
  #     Map.pop!(user_attrs, :org_name)
  #     |> then(fn {org_name, attrs} ->
  #       {role_name, attrs} = Map.pop!(attrs, :role_name)
  #       {status, attrs} = Map.pop(attrs, :status, "active")
  #       {employee_id, attrs} = Map.pop(attrs, :employee_id)
  #       {org_name, role_name, status, employee_id, attrs}
  #     end)

  #   case Repo.get_by(User, email: user_attrs.email) do
  #     nil ->
  #       # Create the user
  #       user =
  #         %User{}
  #         |> User.update_changeset(user_attrs)
  #         |> Repo.insert!()

  #       # Get the org and role
  #       org = Orgs.get_org_by_name(org_name)
  #       role = Roles.get_role_by_name(role_name)

  #       # Create the org-user-role association
  #       changeset_attrs = %{
  #         org_id: org.id,
  #         user_id: user.id,
  #         role_id: role.id,
  #         status: status
  #       }

  #       changeset_attrs =
  #         if employee_id,
  #           do: Map.put(changeset_attrs, :employee_id, employee_id),
  #           else: changeset_attrs

  #       %OrgUserRole{}
  #       |> OrgUserRole.changeset(changeset_attrs)
  #       |> Repo.insert!()

  #       IO.puts("    ✓ Created user: #{user.email} (#{role_name} in #{org_name})")

  #     existing_user ->
  #       # Update the existing user with new data
  #       _updated_user =
  #         existing_user
  #         |> User.update_changeset(user_attrs)
  #         |> Repo.update!()

  #       # Check if user is already associated with the org
  #       org = Orgs.get_org_by_name(org_name)
  #       role = Roles.get_role_by_name(role_name)

  #       case Repo.get_by(OrgUserRole, user_id: existing_user.id, org_id: org.id) do
  #         nil ->
  #           # Add the user to the org with the specified role
  #           changeset_attrs = %{
  #             org_id: org.id,
  #             user_id: existing_user.id,
  #             role_id: role.id,
  #             status: status
  #           }

  #           changeset_attrs =
  #             if employee_id,
  #               do: Map.put(changeset_attrs, :employee_id, employee_id),
  #               else: changeset_attrs

  #           %OrgUserRole{}
  #           |> OrgUserRole.changeset(changeset_attrs)
  #           |> Repo.insert!()

  #           IO.puts(
  #             "    ✓ Updated and added existing user to org: #{existing_user.email} (#{role_name} in #{org_name})"
  #           )

  #         _existing_association ->
  #           IO.puts(
  #             "    ✓ Updated existing user: #{existing_user.email} (#{role_name} in #{org_name})"
  #           )
  #       end
  #   end
  # end
end
