defmodule Codebake.Application do
  # See https://hexdocs.pm/elixir/Application.html
  # for more information on OTP Applications
  @moduledoc false

  use Application

  @impl true
  def start(_type, _args) do
    children = [
      CodebakeWeb.Telemetry,
      Codebake.Repo,
      {DNSCluster, query: Application.get_env(:codebake, :dns_cluster_query) || :ignore},
      {Phoenix.PubSub, name: Codebake.PubSub},
      CodebakeWeb.Presence,
      # Start a worker by calling: Codebake.Worker.start_link(arg)
      # {Codebake.Worker, arg},
      # Start to serve requests, typically the last entry
      CodebakeWeb.Endpoint
    ]

    # See https://hexdocs.pm/elixir/Supervisor.html
    # for other strategies and supported options
    opts = [strategy: :one_for_one, name: Codebake.Supervisor]
    Supervisor.start_link(children, opts)
  end

  # Tell Phoenix to update the endpoint configuration
  # whenever the application is updated.
  @impl true
  def config_change(changed, _new, removed) do
    CodebakeWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end
