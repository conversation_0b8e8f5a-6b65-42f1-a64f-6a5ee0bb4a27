defmodule Codebake.Projects do
  @moduledoc """
  The Projects context.

  Handles all business logic for Projects and Tasks with proper multi-tenant
  authorization and account/project-scoped operations.
  """

  import Ecto.Query, warn: false
  alias Codebake.Repo
  alias Codebake.Accounts.{Account, User}
  alias Codebake.Projects.{Project, ProjectMembership, Task}

  @type phase :: map()

  ## Projects

  @doc """
  Returns the list of projects for an account.
  """
  def list_projects(%Account{} = account, opts \\ []) do
    query =
      from p in Project,
        where: p.account_id == ^account.id,
        order_by: [desc: p.inserted_at]

    query
    |> maybe_filter_by_status(opts[:status])
    |> Repo.all()
  end

  @doc """
  Gets a single project by ID within an account.
  """
  def get_project(%Account{} = account, id) do
    from(p in Project,
      where: p.id == ^id and p.account_id == ^account.id
    )
    |> Repo.one()
  end

  @doc """
  Gets a single project by ID within an account, raising if not found.
  """
  def get_project!(%Account{} = account, id) do
    from(p in Project,
      where: p.id == ^id and p.account_id == ^account.id
    )
    |> Repo.one!()
  end

  @doc """
  Gets a single project by ID (without account scoping).
  """
  def get_project(id) do
    Repo.get(Project, id)
  end

  @doc """
  Gets a single project by ID, raising if not found.
  """
  def get_project!(id) do
    Repo.get!(Project, id)
  end

  @doc """
  Creates a project within an account.

  The creating user is automatically added as an owner-level member of the new
  project so they can manage it immediately.
  """
  @spec create_project(Account.t(), User.t(), map()) ::
          {:ok, Project.t()} | {:error, Ecto.Changeset.t()}
  def create_project(%Account{} = account, %User{} = user, attrs \\ %{}) do
    attrs =
      attrs
      |> Map.put("account_id", account.id)
      |> Map.put("created_by_id", user.id)

    Repo.transaction(fn ->
      with {:ok, project} <- %Project{} |> Project.create_changeset(attrs) |> Repo.insert(),
           {:ok, _membership} <- add_project_member(project, user, user, "owner"),
           {:ok, project} <- add_phase(project, "Initial") do
        project
      else
        {:error, changeset} -> Repo.rollback(changeset)
        {:error, _reason} = err -> Repo.rollback(err)
      end
    end)
  end

  @doc """
  Updates a project.
  """
  def update_project(%Project{} = project, attrs) do
    project
    |> Project.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates a project's task prefix (admin operation).
  """
  def update_project_task_prefix(%Project{} = project, attrs) do
    project
    |> Project.task_prefix_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a project.
  """
  def delete_project(%Project{} = project) do
    Repo.delete(project)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking project changes.
  """
  def change_project(%Project{} = project, attrs \\ %{}) do
    Project.update_changeset(project, attrs)
  end

  ## Project Phases (settings-backed)

  @doc """
  List phases for a project from the settings map.
  Ordered by the optional "position" field.
  """
  @spec list_phases(Project.t()) :: [phase()]
  def list_phases(%Project{settings: settings}) do
    phases = (settings && Map.get(settings, "phases")) || []

    phases
    |> Enum.sort_by(fn p -> Map.get(p, "position", 0) end)
  end

  @doc """
  Get the current phase map from project settings, if set.
  """
  @spec current_phase(Project.t()) :: phase() | nil
  def current_phase(%Project{} = project) do
    settings = project.settings || %{}
    current_id = Map.get(settings, "current_phase_id")

    case current_id do
      nil -> nil
      id -> Enum.find(list_phases(project), fn p -> p["id"] == id end)
    end
  end

  @doc """
  Add a new phase to a project by updating the settings map.
  Returns {:ok, updated_project} | {:error, changeset}
  """
  @spec add_phase(Project.t(), String.t()) :: {:ok, Project.t()} | {:error, Ecto.Changeset.t()}
  def add_phase(%Project{} = project, name) when is_binary(name) do
    trimmed = String.trim(name)

    if trimmed == "" do
      {:error,
       Project.update_changeset(project, %{})
       |> Ecto.Changeset.add_error(:settings, "invalid phase name")}
    else
      settings = project.settings || %{}
      phases = Map.get(settings, "phases", [])

      id = Ecto.UUID.generate()
      position = length(phases)

      new_phase = %{"id" => id, "name" => trimmed, "position" => position}
      new_phases = phases ++ [new_phase]

      settings =
        settings
        |> Map.put("phases", new_phases)
        |> maybe_set_current_phase_id_if_missing(id)

      update_project(project, %{settings: settings})
    end
  end

  def add_phase(%Project{} = project, _name) do
    {:error,
     Project.update_changeset(project, %{})
     |> Ecto.Changeset.add_error(:settings, "invalid phase name")}
  end

  defp maybe_set_current_phase_id_if_missing(settings, new_phase_id) do
    case Map.get(settings, "current_phase_id") do
      nil -> Map.put(settings, "current_phase_id", new_phase_id)
      _ -> settings
    end
  end

  @doc """
  Set the current phase by id. Validates the id exists in the project's phases.
  Returns {:ok, updated_project} | {:error, :not_found | Ecto.Changeset.t()}
  """
  @spec set_current_phase(Project.t(), String.t()) :: {:ok, Project.t()} | {:error, term()}
  def set_current_phase(%Project{} = project, phase_id) when is_binary(phase_id) do
    case Enum.find(list_phases(project), fn p -> p["id"] == phase_id end) do
      nil ->
        {:error, :not_found}

      _ ->
        settings = project.settings || %{}
        update_project(project, %{settings: Map.put(settings, "current_phase_id", phase_id)})
    end
  end

  ## Project Memberships

  @doc """
  Check if user is a member of the given project.
  """
  def project_member?(%User{} = user, %Project{} = project) do
    from(pm in ProjectMembership,
      where: pm.user_id == ^user.id and pm.project_id == ^project.id
    )
    |> Repo.exists?()
  end

  @doc """
  Get user's role in a project.
  """
  def get_user_role_in_project(%User{} = user, %Project{} = project) do
    from(pm in ProjectMembership,
      where: pm.user_id == ^user.id and pm.project_id == ^project.id,
      select: pm.role
    )
    |> Repo.one()
  end

  @doc """
  List all projects for a user across all accounts.
  """
  def list_user_projects(%User{} = user, opts \\ []) do
    query =
      from p in Project,
        join: pm in ProjectMembership,
        on: pm.project_id == p.id,
        where: pm.user_id == ^user.id,
        preload: [:account],
        order_by: [desc: p.inserted_at]

    query
    |> maybe_filter_by_account(opts[:account_id])
    |> maybe_filter_by_status(opts[:status])
    |> Repo.all()
  end

  @doc """
  List all projects for an account.
  """
  def list_account_projects(%Account{} = account) do
    from(p in Project,
      where: p.account_id == ^account.id,
      order_by: [desc: p.inserted_at],
      preload: [:created_by]
    )
    |> Repo.all()
  end

  @doc """
  List projects for an account with user's role information.
  """
  def list_account_projects_with_user_roles(%Account{} = account, %User{} = user) do
    from(p in Project,
      left_join: pm in ProjectMembership,
      on: pm.project_id == p.id and pm.user_id == ^user.id,
      where: p.account_id == ^account.id,
      select: %{
        project: p,
        user_role: pm.role,
        is_member: not is_nil(pm.id)
      },
      order_by: [desc: p.inserted_at]
    )
    |> Repo.all()
  end

  @doc """
  Add a user to a project with a specific role.
  """
  def add_project_member(
        %Project{} = project,
        %User{} = user,
        %User{} = invited_by,
        role \\ "member"
      ) do
    %ProjectMembership{}
    |> ProjectMembership.invite_changeset(%{
      project_id: project.id,
      user_id: user.id,
      invited_by_id: invited_by.id,
      role: role
    })
    |> Repo.insert()
  end

  @doc """
  Update a user's role in a project.
  """
  def update_project_member_role(%User{} = user, %Project{} = project, new_role) do
    case get_project_membership(user, project) do
      nil ->
        {:error, :not_found}

      membership ->
        membership
        |> ProjectMembership.role_changeset(%{role: new_role})
        |> Repo.update()
    end
  end

  @doc """
  Remove a user from a project.
  """
  def remove_project_member(%User{} = user, %Project{} = project) do
    case get_project_membership(user, project) do
      nil -> {:error, :not_found}
      membership -> Repo.delete(membership)
    end
  end

  @doc """
  Get project membership record for a user and project.
  """
  def get_project_membership(%User{} = user, %Project{} = project) do
    from(pm in ProjectMembership,
      where: pm.user_id == ^user.id and pm.project_id == ^project.id
    )
    |> Repo.one()
  end

  @doc """
  List all users who are members of the given project.
  """
  @spec list_project_members(Project.t()) :: [User.t()]
  def list_project_members(%Project{} = project) do
    from(u in User,
      join: pm in ProjectMembership,
      on: pm.user_id == u.id and pm.project_id == ^project.id,
      order_by: [asc: u.email]
    )
    |> Repo.all()
  end

  ## Tasks

  @doc """
  Returns the list of tasks for a project.
  """
  def list_tasks(%Project{} = project, opts \\ []) do
    query =
      from t in Task,
        where: t.project_id == ^project.id,
        order_by: [asc: t.task_number]

    query
    |> maybe_filter_by_status(opts[:status])
    |> maybe_filter_by_assignee(opts[:assignee_id])
    |> maybe_filter_by_parent(opts[:parent_task_id])
    |> maybe_preload_associations(opts[:preload])
    |> Repo.all()
  end

  @doc """
  Returns the list of tasks assigned to a user across all projects in an account.
  """
  def list_user_tasks(%Account{} = account, %User{} = user, opts \\ []) do
    query =
      from t in Task,
        join: p in Project,
        on: t.project_id == p.id,
        where: p.account_id == ^account.id and t.assignee_id == ^user.id,
        order_by: [desc: t.inserted_at]

    query
    |> maybe_filter_by_status(opts[:status])
    |> maybe_preload_associations(opts[:preload] || [:project])
    |> Repo.all()
  end

  @doc """
  Returns the list of tasks assigned to a user across all projects they have access to.
  """
  def list_all_user_tasks(%User{} = user, opts \\ []) do
    query =
      from t in Task,
        join: p in Project,
        on: t.project_id == p.id,
        join: pm in ProjectMembership,
        on: pm.project_id == p.id,
        where: pm.user_id == ^user.id and t.assignee_id == ^user.id,
        order_by: [desc: t.inserted_at]

    query
    |> maybe_filter_by_status(opts[:status])
    |> maybe_preload_associations(opts[:preload] || [:project])
    |> Repo.all()
  end

  @doc """
  Gets a single task by ID within a project.
  """
  def get_task(%Project{} = project, id) do
    from(t in Task,
      where: t.id == ^id and t.project_id == ^project.id
    )
    |> Repo.one()
  end

  @doc """
  Gets a single task by ID within a project, raising if not found.
  """
  def get_task!(%Project{} = project, id) do
    from(t in Task,
      where: t.id == ^id and t.project_id == ^project.id
    )
    |> Repo.one!()
  end

  @doc """
  Creates a task within a project.
  """
  def create_task(%Project{} = project, %User{} = user, attrs \\ %{}) do
    Repo.transaction(fn ->
      # Lock the project row to prevent race conditions
      locked_project =
        from(p in Project, where: p.id == ^project.id, lock: "FOR UPDATE")
        |> Repo.one!()

      # Get the next task number and increment the project counter
      task_number = Project.next_task_number(locked_project)
      task_identifier = Project.generate_task_identifier(locked_project, task_number)

      # Update project task counter
      {:ok, _updated_project} =
        locked_project
        |> Project.increment_task_counter_changeset()
        |> Repo.update()

      # Create the task with the assigned number and append position in its status column
      status = Map.get(attrs, "status", "todo")

      # Determine next position at end of the status column
      next_pos =
        from(t in Task,
          where: t.project_id == ^project.id and t.status == ^status,
          select: fragment("COALESCE(MAX(?), 0)", t.position)
        )
        |> Repo.one()
        |> Kernel.+(1)

      # Ensure a phase is set, defaulting to the project's current phase
      phase_id =
        case current_phase(locked_project) do
          %{} = phase -> phase["id"]
          _ -> nil
        end

      attrs =
        attrs
        |> Map.put("project_id", project.id)
        |> Map.put("created_by_id", user.id)
        |> Map.put("position", next_pos)
        |> Map.put_new("phase_id", phase_id)

      task_result =
        %Task{}
        |> Task.create_changeset(attrs)
        |> Task.set_task_number_changeset(task_number, task_identifier)
        |> Repo.insert()

      case task_result do
        {:ok, task} -> task
        {:error, changeset} -> Repo.rollback(changeset)
      end
    end)
  end

  @doc """
  Updates a task.
  """
  def update_task(%Task{} = task, attrs) do
    task
    |> Task.update_changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Updates a task's status.
  """
  def update_task_status(%Task{} = task, status) do
    task
    |> Task.status_changeset(status)
    |> Repo.update()
  end

  @doc """
  Move a task to a new status and reorder within that status.

  If `before_id` is provided and refers to a task in the same project and target status,
  the moved task will be placed directly before that task. Otherwise, it will be appended
  to the end of the target column. Positions are re-numbered contiguously starting at 1
  for the target column. If moving across columns, the source column is also compacted
  to maintain contiguous positions.
  """
  @spec reorder_and_move_task(Project.t(), Task.t(), String.t(), String.t() | nil) ::
          {:ok, Task.t()} | {:error, term()}
  def reorder_and_move_task(%Project{} = project, %Task{} = task, new_status, before_id),
    do: reorder_and_move_task(project, task, new_status, before_id, [])

  @doc """
  Same as reorder_and_move_task/4, with options.

  Options:
  - assign_if_unassigned_to: user_id – when moving into "in_progress", if the task has no assignee,
    set assignee_id to this user id as part of the same transaction.
  """
  @spec reorder_and_move_task(Project.t(), Task.t(), String.t(), String.t() | nil, keyword()) ::
          {:ok, Task.t()} | {:error, term()}
  def reorder_and_move_task(%Project{} = project, %Task{} = task, new_status, before_id, opts)
      when is_binary(new_status) and is_list(opts) do
    Repo.transaction(fn ->
      # Lock target column tasks for update
      target_tasks =
        from(t in Task,
          where: t.project_id == ^project.id and t.status == ^new_status,
          order_by: [asc: t.position, asc: t.task_number],
          lock: "FOR UPDATE"
        )
        |> Repo.all()

      # Remove task if it already exists in target list
      target_wo_task = Enum.reject(target_tasks, &(&1.id == task.id))

      # Determine insertion index
      insert_index =
        case before_id && Enum.find_index(target_wo_task, &(&1.id == before_id)) do
          # append
          nil -> length(target_wo_task)
          idx -> idx
        end

      # Build the new ordered list with the moved task included (keep original struct to ensure status change persists)
      new_target_list =
        target_wo_task
        |> List.insert_at(insert_index, task)

      # If moving across columns, compact the source column positions as well
      if task.status != new_status do
        from(s in Task,
          where: s.project_id == ^project.id and s.status == ^task.status,
          order_by: [asc: s.position, asc: s.task_number],
          lock: "FOR UPDATE"
        )
        |> Repo.all()
        |> Enum.reject(&(&1.id == task.id))
        |> Enum.with_index(1)
        |> Enum.each(fn {t, pos} ->
          {:ok, _} = Repo.update(Ecto.Changeset.change(t, %{position: pos}))
        end)
      end

      # Persist target positions and status for all tasks in the new target list
      moved_id = task.id
      orig_status = task.status
      assign_to_id = Keyword.get(opts, :assign_if_unassigned_to)

      persisted_task =
        new_target_list
        |> Enum.with_index(1)
        |> Enum.reduce(nil, fn {t, pos}, acc ->
          base = %{position: pos}

          changes =
            base
            |> maybe_put_status(t, moved_id, orig_status, new_status)
            |> maybe_put_assignee(t, moved_id, orig_status, new_status, assign_to_id)
            |> maybe_clear_assignee(
              t,
              moved_id,
              orig_status,
              new_status,
              Keyword.get(opts, :unassign_if_moved_to_todo)
            )

          {:ok, updated} = Repo.update(Ecto.Changeset.change(t, changes))
          if t.id == moved_id, do: updated, else: acc
        end)

      case persisted_task do
        %Task{} = t -> t
        _ -> Repo.rollback(:failed_to_persist)
      end
    end)
  end

  defp maybe_put_status(map, t, moved_id, orig_status, new_status) do
    if t.id == moved_id and orig_status != new_status,
      do: Map.put(map, :status, new_status),
      else: map
  end

  defp maybe_put_assignee(map, t, moved_id, orig_status, new_status, assign_to_id) do
    cond do
      is_nil(assign_to_id) -> map
      t.id != moved_id -> map
      orig_status == new_status -> map
      new_status != "in_progress" -> map
      not is_nil(t.assignee_id) -> map
      true -> Map.put(map, :assignee_id, assign_to_id)
    end
  end

  defp maybe_clear_assignee(map, t, moved_id, orig_status, new_status, unassign?) do
    cond do
      unassign? != true -> map
      t.id != moved_id -> map
      orig_status == new_status -> map
      new_status != "todo" -> map
      is_nil(t.assignee_id) -> map
      true -> Map.put(map, :assignee_id, nil)
    end
  end

  @doc """
  Deletes a task.
  """
  def delete_task(%Task{} = task) do
    Repo.delete(task)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking task changes.
  """
  def change_task(%Task{} = task, attrs \\ %{}) do
    Task.update_changeset(task, attrs)
  end

  ## Private helpers

  defp maybe_filter_by_status(query, nil), do: query

  defp maybe_filter_by_status(query, status) do
    from q in query, where: q.status == ^status
  end

  defp maybe_filter_by_account(query, nil), do: query

  defp maybe_filter_by_account(query, account_id) do
    from q in query, where: q.account_id == ^account_id
  end

  defp maybe_filter_by_assignee(query, nil), do: query

  defp maybe_filter_by_assignee(query, assignee_id) do
    from q in query, where: q.assignee_id == ^assignee_id
  end

  defp maybe_filter_by_parent(query, nil), do: query

  defp maybe_filter_by_parent(query, :root) do
    from q in query, where: is_nil(q.parent_task_id)
  end

  defp maybe_filter_by_parent(query, parent_task_id) do
    from q in query, where: q.parent_task_id == ^parent_task_id
  end

  defp maybe_preload_associations(query, nil), do: query

  defp maybe_preload_associations(query, preloads) when is_list(preloads) do
    from q in query, preload: ^preloads
  end
end
