# Account Onboarding Implementation

## Overview

I've implemented a comprehensive account onboarding flow that prompts new users to set up their account name (like "Misfits") after they first log in, instead of using auto-generated account names.

## How It Works

### 1. **User Login Flow**
```
User logs in → Check if account setup needed → Redirect to onboarding OR main app
```

### 2. **Account Setup Detection**
The system checks if a user needs account setup by looking for:
- **No account associated** with the user (`account_id` is `nil`)
- **Auto-generated account names** like:
  - `"<EMAIL>'s Account"`
  - `"user-123"` (slug-based names)
  - Any name containing `@` or `'s Account`

### 3. **Onboarding Experience**
When a user needs setup, they're redirected to `/onboarding/account` where they can:
- **Set company name** (e.g., "Misfits")
- **Choose billing email** (defaults to their email)
- **Select subscription tier** (freemium, team, business, enterprise)
- **Preview URL structure** (shows `/misfits` as they type)
- **Skip for now** (if they want to set up later)

## Implementation Details

### New Routes Added
```elixir
# Onboarding routes (no account setup check)
live_session :onboarding,
  on_mount: [{CodebakeWeb.UserAuth, :require_authenticated}] do
  live "/onboarding/account", OnboardingLive.Account, :new
end

# Main app routes (with account setup check)
live_session :require_authenticated_user,
  on_mount: [{CodebakeWeb.UserAuth, :require_account_setup}] do
  # All team/project routes that require proper account setup
end
```

### New Authentication Hook
```elixir
def on_mount(:require_account_setup, _params, session, socket) do
  socket = mount_current_scope(socket, session)

  if socket.assigns.current_scope && socket.assigns.current_scope.user do
    user = socket.assigns.current_scope.user
    
    if Accounts.user_needs_account_setup?(user) do
      socket =
        socket
        |> Phoenix.LiveView.put_flash(:info, "Let's set up your account to get started!")
        |> Phoenix.LiveView.redirect(to: ~p"/onboarding/account")

      {:halt, socket}
    else
      {:cont, socket}
    end
  end
end
```

### Account Setup Detection Logic
```elixir
def user_needs_account_setup?(%{account_id: nil}), do: true

def user_needs_account_setup?(%{account_id: account_id}) when not is_nil(account_id) do
  account = get_account!(account_id)
  is_generated_account_name?(account.name)
end

defp is_generated_account_name?(name) when is_binary(name) do
  String.contains?(name, "'s Account") or 
  String.contains?(name, "@") or
  String.match?(name, ~r/^[a-z]+-\d+$/)
end
```

### Updated Login Redirects
```elixir
def signed_in_path(%Plug.Conn{assigns: %{current_scope: %Scope{user: %Accounts.User{} = user}}}) do
  if Accounts.user_needs_account_setup?(user) do
    ~p"/onboarding/account"
  else
    ~p"/teams"
  end
end
```

## User Experience Flow

### First-Time User Journey
1. **User registers/logs in** → Redirected to `/onboarding/account`
2. **Sees welcome message**: "Welcome to Codebake! Let's set up your account to get started"
3. **Fills out form**:
   - Company name: "Misfits"
   - Billing email: "<EMAIL>" 
   - Plan: "Team (up to 10 members)"
4. **Sees URL preview**: "Your account URL will be: `/misfits`"
5. **Clicks "Create Account"** → Redirected to `/misfits` (account dashboard)
6. **Can now create teams** under the "Misfits" account

### Existing User with Auto-Generated Account
1. **User with account like "<EMAIL>'s Account"** → Redirected to onboarding
2. **Form pre-filled** with existing account data
3. **Button shows "Update Account"** instead of "Create Account"
4. **Updates account name** to "Misfits" → Redirected to `/misfits`

### Skip Option
- Users can click **"Skip for now"** to bypass onboarding
- They'll be redirected to `/teams` with a message: "You can set up your account later"
- They can manually create a proper account via "Create Account" button on teams page

## Routes That Require Account Setup

The following routes now check for account setup and redirect to onboarding if needed:
- `/teams` - Team management
- `/:account_slug` - Account dashboard  
- `/:account_slug/:team_slug` - Team dashboard
- `/:account_slug/:team_slug/projects` - Project management
- All other team/project routes

## Routes That DON'T Require Account Setup

These routes allow access without account setup:
- `/users/settings` - User profile settings
- `/users/settings/confirm-email/:token` - Email confirmation
- `/users/settings/tokens` - Personal access tokens
- `/onboarding/account` - The onboarding page itself

## Benefits

✅ **Proper Company Accounts**: Users create accounts like "Misfits" instead of "<EMAIL>'s Account"
✅ **Clean URLs**: Results in `/misfits/engineering` instead of `/user-123/engineering`  
✅ **Better UX**: Guided onboarding experience with clear explanations
✅ **Flexible**: Users can skip and set up later if needed
✅ **Backward Compatible**: Existing users with proper accounts aren't affected
✅ **Smart Detection**: Automatically detects auto-generated vs. proper account names

## Testing

- **11 onboarding tests** covering all scenarios
- **Updated existing tests** to use proper account fixtures
- **Comprehensive coverage** of detection logic, form validation, and redirect flows
- **Integration tests** verify the complete user journey

## Next Steps

The onboarding flow is now complete! When you deploy this, new users will be guided through setting up their company account properly, resulting in the clean, professional URL structure you wanted (`/misfits/engineering` instead of auto-generated names).

Users can also manually create additional accounts via the "Create Account" button on the teams page if they need to manage multiple organizations.
