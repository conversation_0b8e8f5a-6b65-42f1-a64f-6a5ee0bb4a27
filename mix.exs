defmodule Codebake.MixProject do
  use Mix.Project

  def project do
    [
      app: :codebake,
      version: "0.1.0",
      elixir: "~> 1.15",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      listeners: [Phoenix.CodeReloader]
    ]
  end

  # Configuration for the OTP application.
  #
  # Type `mix help compile.app` for more information.
  def application do
    [
      mod: {Codebake.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  def cli do
    [
      preferred_envs: [precommit: :test]
    ]
  end

  # Specifies which paths to compile per environment.
  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  # Specifies your project dependencies.
  #
  # Type `mix help deps` for examples and options.
  defp deps do
    [
      {:bcrypt_elixir, "~> 3.0"},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:tidewave, "~> 0.4", only: [:dev]},
      {:usage_rules, "~> 0.1", only: [:dev]},
      {:igniter, "~> 0.6", only: [:dev, :test]},
      {:phoenix, "~> 1.8.1"},
      {:phoenix_ecto, "~> 4.5"},
      {:ecto_sql, "~> 3.13"},
      {:postgrex, ">= 0.0.0"},
      {:phoenix_html, "~> 4.1"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 1.1.0"},
      {:lazy_html, ">= 0.1.0", only: :test},
      {:phoenix_live_dashboard, "~> 0.8.3"},
      {:esbuild, "~> 0.10", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.3", runtime: Mix.env() == :dev},
      {:heroicons,
       github: "tailwindlabs/heroicons",
       tag: "v2.2.0",
       sparse: "optimized",
       app: false,
       compile: false,
       depth: 1},
      {:swoosh, "~> 1.16"},
      {:req, "~> 0.5"},
      {:telemetry_metrics, "~> 1.0"},
      {:telemetry_poller, "~> 1.0"},
      {:gettext, "~> 0.26"},
      {:jason, "~> 1.2"},
      {:dns_cluster, "~> 0.2.0"},
      {:bandit, "~> 1.5"},
      {:uuidv7, "~> 1.0"},
      {:ecto_erd, "~> 0.6", only: :dev},
      # S3 dependencies for attachment system
      {:ex_aws, "~> 2.5"},
      {:ex_aws_s3, "~> 2.5"},
      {:hackney, "~> 1.20"},
      {:sweet_xml, "~> 0.7"},
      {:nimble_csv, "~> 1.2"},
      # Markdown processing for notes system
      {:earmark, "~> 1.4"},
      {:html_sanitize_ex, "~> 1.4"},
      # Authorization library
      {:bodyguard, "~> 2.4"},
      # Test factories
      {:ex_machina, "~> 2.8.0", only: :test}
    ]
  end

  # See the documentation for `Mix` for more info on aliases.
  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind codebake", "esbuild codebake"],
      "assets.deploy": [
        "tailwind codebake --minify",
        "esbuild codebake --minify",
        "phx.digest"
      ],
      # Migration aliases
      "db.migrate": ["ecto.migrate"],
      # ERD generation aliases
      "ecto.migrate.erd": ["ecto.migrate", "erd"],
      "ecto.seed": "run priv/repo/seeds.exs",
      erd: [
        "cmd mkdir -p docs",
        "ecto.gen.erd --output-path=docs/ecto_erd.dot",
        "cmd dot -Tsvg docs/ecto_erd.dot -o docs/erd.svg",
        "cmd rm docs/ecto_erd.dot"
      ],
      # Credo aliases for static code analysis
      "code.check": ["format --check-formatted", "credo --strict"],
      "code.fix": ["format", "credo --strict"],
      precommit: ["format --check-formatted", "credo --strict", "test"],
      # Docker-related aliases
      "docker.up": "cmd docker-compose up -d",
      "docker.dev": "cmd docker-compose up app",
      "docker.app-restart": "cmd docker-compose restart app",
      "docker.down": "cmd docker-compose down",
      "docker.setup": &docker_setup/1,
      "docker.test": &docker_test/1,
      "docker.reset": [
        "cmd docker-compose down --volumes --remove-orphans",
        "cmd docker network prune -f",
        "cmd docker container prune -f",
        "cmd docker system prune -f",
        "cmd docker volume rm codebake_deps codebake_build codebake_build_test codebake_node_modules 2>/dev/null || true",
        "docker.up"
      ],
      "docker.db-reset": "cmd docker-compose --profile tools up db-reset",
      "docker.seed": &docker_seed/1,
      "docker.erd": "cmd docker-compose --profile tools up erd",
      "docker.monitor": "cmd docker-compose --profile tools up pg-monitor",
      "docker.logs": "cmd docker-compose logs -f",
      "docker.shell": "cmd docker-compose exec app bash"
    ]
  end

  # Custom function to handle docker test with optional arguments
  defp docker_test(args) do
    case args do
      [] ->
        # No arguments, run all tests
        Mix.shell().cmd("docker-compose --profile tools up test")

      _ ->
        # Arguments provided, run specific tests
        test_args = Enum.join(args, " ")

        Mix.shell().cmd("""
        docker-compose --profile tools run --rm test bash -c "
          mix deps.get
          mix test --trace --timeout 120000 #{test_args} --color
        "
        """)
    end
  end

  # Custom function to handle docker seeding
  defp docker_seed(args) do
    case args do
      [] ->
        # No arguments, run the standard seeder service
        Mix.shell().cmd("docker-compose --profile tools up seeder")

      ["--reset"] ->
        # Reset database and then seed
        Mix.shell().cmd("docker-compose --profile tools up db-reset")

      ["--fresh"] ->
        # Fresh seed without reset (just run seeds on existing DB)
        Mix.shell().cmd("""
        docker-compose --profile tools run --rm seeder bash -c "
          mix deps.get
          echo 'Running fresh seeds on existing database...'
          mix run priv/repo/seeds.exs
          echo 'Fresh seeding completed!'
        "
        """)

      _ ->
        # Invalid arguments
        Mix.shell().info("""
        Usage: mix codebake.seed.docker [options]

        Options:
          (no args)  - Run standard seeder (creates DB if needed, migrates, then seeds)
          --reset    - Reset database completely and then seed
          --fresh    - Run seeds on existing database without reset/migration
        """)
    end
  end

  # Custom function to handle docker setup (create/migrate DB inside docker app container)
  defp docker_setup(_args) do
    # Ensure core services are ready
    start_code = Mix.shell().cmd("docker-compose up -d postgres localstack")

    if start_code != 0 do
      Mix.raise(
        "Failed to start docker services (postgres/localstack). Check docker-compose output."
      )
    end

    # Run DB create/migrate in app container environment
    cmd = """
    docker-compose run --rm app bash -lc "
      mix deps.get
      mix ecto.create
      mix ecto.migrate
      mix run priv/repo/seeds.exs
    "
    """

    exit_code = Mix.shell().cmd(cmd)

    if exit_code != 0 do
      Mix.raise("docker.setup failed. See output above for details.")
    end

    :ok
  end
end
