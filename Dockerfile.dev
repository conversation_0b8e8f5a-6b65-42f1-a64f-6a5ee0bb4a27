# Use the same Elixir version as in the main Dockerfile
FROM hexpm/elixir:1.18.4-erlang-28.0.2-debian-bookworm-20250630-slim

# Install build dependencies and graphviz for dot command
RUN apt-get update -y && \
  apt-get install -y \
    build-essential git inotify-tools graphviz \
    && apt-get clean && rm -f /var/lib/apt/lists/*_*

# Set working directory
WORKDIR /app    

# Install hex + rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Set environment variables
ENV MIX_ENV=dev
ENV ELIXIR_ERL_OPTIONS="-kernel shell_history enabled"
ENV MIX_HOME=/app/.mix
# Force lazy_html to compile from source instead of using precompiled binaries
ENV ELIXIR_MAKE_CACHE_DIR=""

# Copy mix files first to leverage Docker caching
COPY mix.exs mix.lock ./

# Install dependencies with explicit environment
RUN mix deps.get --only dev

# Copy the rest of the application code
COPY . .

# Install and setup assets
RUN mix assets.setup

# Expose the Phoenix ports
EXPOSE 4000

# Start the Phoenix server
CMD ["mix", "phx.server"]
