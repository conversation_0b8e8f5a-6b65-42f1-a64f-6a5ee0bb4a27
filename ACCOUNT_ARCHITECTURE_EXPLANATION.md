# Account Architecture Implementation

## Overview

I've implemented a proper multi-tenant account architecture that matches your requirements. Here's how accounts, users, teams, and projects now relate to each other.

## Current Architecture

```
Account: "Misfits" (slug: "misfits")
├── User: <EMAIL> (billing member/admin)
├── Teams:
│   ├── Engineering (3 projects)
│   │   ├── Project: API Redesign
│   │   ├── Project: Mobile App
│   │   └── Project: Database Migration
│   ├── Marketing (1 project)
│   │   └── Project: Website Redesign
│   └── Sales (2 projects)
│       ├── Project: CRM Integration
│       └── Project: Lead Tracking
└── Other Users: (invited team members)
    ├── <EMAIL> (Engineering team member)
    └── <EMAIL> (Marketing team admin)
```

## Relationships

### Account ↔ User
- **Users belong to accounts** through team memberships
- **Account owners/admins** can manage the entire account
- **Billing members** are associated with the account for subscription management

### Account ↔ Team
- **Teams belong to accounts** (one-to-many)
- **Account slug + team slug** creates unique URLs like `/misfits/engineering`
- **Team names are unique within an account** but can be duplicated across accounts

### Team ↔ User
- **Users join teams** through team memberships with roles (viewer, member, admin, owner)
- **Users can be in multiple teams** within the same account
- **User's account role** is the highest role across all their team memberships

### Team ↔ Project
- **Projects belong to teams** (one-to-many)
- **Projects inherit account context** through their team

## New Features Implemented

### 1. Account Dashboard (`/misfits`)
- **Overview of all teams** in the account
- **Project counts per team**
- **Account statistics** (total teams, total projects, active teams)
- **Team management links** with proper URL structure

### 2. Account Creation (`/accounts/new`)
- **Create company accounts** like "Misfits" with slug "misfits"
- **Choose subscription tier** (freemium, team, business, enterprise)
- **Set billing email** for account management
- **Preview URL structure** before creation

### 3. Enhanced Team Management
- **Teams show account context** in the teams list
- **Account-scoped team creation** 
- **Proper URL structure** using account and team slugs

### 4. URL Structure
```
/misfits                           # Account dashboard
/misfits/engineering               # Team dashboard  
/misfits/engineering/settings      # Team settings
/misfits/engineering/projects      # Team projects
/misfits/engineering/projects/new  # New project modal
```

## Database Schema

### Accounts Table
```sql
accounts:
  id (UUID, primary key)
  name (string) - "Misfits"
  slug (string, unique) - "misfits" 
  billing_email (string)
  subscription_tier (string)
  created_at, updated_at
```

### Teams Table  
```sql
teams:
  id (UUID, primary key)
  account_id (UUID, foreign key) 
  name (string) - "Engineering"
  slug (string) - "engineering"
  description (text)
  created_at, updated_at
  
  UNIQUE INDEX: (account_id, slug)
```

### Users Table
```sql
users:
  id (UUID, primary key)
  email (string, unique)
  name (string)
  created_at, updated_at
```

### Team Memberships Table
```sql
team_memberships:
  id (UUID, primary key)
  team_id (UUID, foreign key)
  user_id (UUID, foreign key) 
  role (string) - "viewer", "member", "admin", "owner"
  created_at, updated_at
  
  UNIQUE INDEX: (team_id, user_id)
```

## Key Functions Added

### Account Management
- `Accounts.create_account/1` - Create new accounts
- `Accounts.get_account_by_slug/1` - Find accounts by slug
- `Accounts.list_account_teams_with_project_counts/1` - Dashboard data
- `Accounts.get_account_stats/1` - Account statistics
- `Accounts.account_member?/2` - Check if user belongs to account
- `Accounts.get_user_account_role/2` - Get user's highest role in account

### Authorization
- `load_and_authorize_account_lv/3` - Account-level authorization
- Enhanced team authorization to include account context

## User Workflow

### 1. Creating an Account
1. User clicks "Create Account" from teams page
2. Fills out account details (name: "Misfits", billing email, plan)
3. System generates slug "misfits" and creates account
4. User becomes account owner/admin
5. Redirected to account dashboard `/misfits`

### 2. Account Dashboard Experience  
1. Visit `/misfits` to see account overview
2. View all teams with project counts
3. See account statistics (teams, projects, active teams)
4. Click team cards to navigate to team dashboards
5. Create new teams or manage account settings

### 3. Team Management
1. Teams are created within account context
2. URLs use account/team slug format: `/misfits/engineering`
3. Team members see account name in team cards
4. Navigation maintains account context throughout

## Benefits

✅ **Proper Multi-Tenancy**: Clear account boundaries with no data leakage
✅ **Human-Readable URLs**: `/misfits/engineering/projects` instead of UUIDs
✅ **Scalable Architecture**: Supports multiple accounts with isolated data
✅ **Clear Ownership**: Account owners can manage all teams and billing
✅ **Flexible Team Structure**: Teams can have different purposes within accounts
✅ **SEO Friendly**: Clean URL structure for better discoverability

## Next Steps

To complete the implementation, you might want to add:
- Account settings page for managing billing and subscription
- User invitation system at the account level
- Account-level permissions and roles
- Billing integration with subscription management
- Account usage analytics and reporting
